"""
同步任务模块
包含与数据同步相关的Flask-RQ任务
"""

import logging
import uuid
from ..core.task_manager import job
from ..core.debug import get_app_logger
from ..core.task_monitor import get_task_monitor

# 获取日志记录器
logger = get_app_logger("sync_tasks")

# 获取任务监控实例
task_monitor = get_task_monitor()

@job(queue='default', timeout=1800, result_ttl=86400, description="材料数据同步任务")
def sync_materials():
    """
    同步材料数据的RQ任务
    """
    # 生成任务ID
    task_id = str(uuid.uuid4())
    logger.info(f"开始执行材料数据同步任务 (任务ID: {task_id})")

    # 更新任务状态为STARTED
    task_monitor.update_task_status(task_id, "STARTED")

    try:
        # 导入同步服务模块
        from ..core.sync_service import sync_materials_data

        # 执行同步操作
        result = sync_materials_data()

        logger.info(f"材料数据同步任务完成 (任务ID: {task_id})")

        # 更新任务状态为SUCCESS
        task_monitor.update_task_status(task_id, "SUCCESS", result=result)

        return {"status": "success", "message": "材料数据同步完成", "result": result, "task_id": task_id}

    except Exception as e:
        error_msg = f"材料数据同步任务失败 (任务ID: {task_id}): {str(e)}"
        logger.error(error_msg, exc_info=True)

        # 更新任务状态为FAILURE
        task_monitor.update_task_status(task_id, "FAILURE", error=error_msg)

        # 重新抛出异常，让RQ记录失败
        raise

@job(queue='default', timeout=600, result_ttl=86400, description="单个材料数据处理任务")
def process_material_data(material_id):
    """
    处理单个材料数据的RQ任务

    Args:
        material_id: 材料ID
    """
    # 生成任务ID
    task_id = str(uuid.uuid4())
    logger.info(f"开始处理材料数据 (材料ID: {material_id}, 任务ID: {task_id})")

    # 更新任务状态为STARTED
    task_monitor.update_task_status(task_id, "STARTED")

    try:
        # 这里实现处理单个材料数据的逻辑
        # 例如：更新向量索引、计算统计数据等

        # 模拟处理结果
        result = {
            "material_id": material_id,
            "processed": True,
            "vector_updated": True
        }

        logger.info(f"材料数据处理完成 (材料ID: {material_id}, 任务ID: {task_id})")

        # 更新任务状态为SUCCESS
        task_monitor.update_task_status(task_id, "SUCCESS", result=result)

        return {"status": "success", "message": f"材料 {material_id} 处理完成", "result": result, "task_id": task_id}

    except Exception as e:
        error_msg = f"材料数据处理失败 (材料ID: {material_id}, 任务ID: {task_id}): {str(e)}"
        logger.error(error_msg, exc_info=True)

        # 更新任务状态为FAILURE
        task_monitor.update_task_status(task_id, "FAILURE", error=error_msg)

        # 重新抛出异常，让RQ记录失败
        raise
