"""
任务监控模块
用于监控和管理RQ任务
"""

import redis
import json
import logging
from typing import List, Dict, Any, Optional
from ..config import REDIS_HOST, REDIS_PORT, REDIS_PASSWORD, REDIS_DB
from .rq_app import get_redis_connection_for_monitor, get_job
from .debug import get_app_logger

# 获取日志记录器
logger = get_app_logger("task_monitor")

# Redis键前缀
ACTIVE_TASKS_KEY = "rq:active_tasks"
COMPLETED_TASKS_KEY = "rq:completed_tasks"
TASK_DETAILS_KEY_PREFIX = "rq:task:"

# 最大保存的已完成任务数量
MAX_COMPLETED_TASKS = 100

class TaskMonitor:
    """任务监控类"""

    def __init__(self):
        """初始化任务监控"""
        self.redis_client = get_redis_connection_for_monitor()

    def register_task(self, task_id: str, task_name: str) -> None:
        """
        注册新任务

        Args:
            task_id: 任务ID
            task_name: 任务名称
        """
        try:
            # 添加到活跃任务集合
            self.redis_client.sadd(ACTIVE_TASKS_KEY, task_id)

            # 保存任务详情
            task_details = {
                "task_id": task_id,
                "task_name": task_name,
                "status": "PENDING",
                "created_at": self._get_current_timestamp()
            }

            self.redis_client.set(
                f"{TASK_DETAILS_KEY_PREFIX}{task_id}",
                json.dumps(task_details),
                ex=86400  # 24小时过期
            )

            logger.info(f"任务已注册: {task_id} ({task_name})")

        except Exception as e:
            logger.error(f"注册任务时出错: {str(e)}", exc_info=True)

    def update_task_status(self, task_id: str, status: str, result: Any = None, error: str = None) -> None:
        """
        更新任务状态

        Args:
            task_id: 任务ID
            status: 任务状态
            result: 任务结果
            error: 错误信息
        """
        try:
            # 获取任务详情
            task_details_json = self.redis_client.get(f"{TASK_DETAILS_KEY_PREFIX}{task_id}")

            if task_details_json:
                task_details = json.loads(task_details_json)
                task_details["status"] = status
                task_details["updated_at"] = self._get_current_timestamp()

                if result is not None:
                    task_details["result"] = result

                if error is not None:
                    task_details["error"] = error

                # 更新任务详情
                self.redis_client.set(
                    f"{TASK_DETAILS_KEY_PREFIX}{task_id}",
                    json.dumps(task_details),
                    ex=86400  # 24小时过期
                )

                # 如果任务已完成，从活跃任务中移除并添加到已完成任务
                if status in ["SUCCESS", "FAILURE", "REVOKED"]:
                    self.redis_client.srem(ACTIVE_TASKS_KEY, task_id)

                    # 添加到已完成任务列表（使用有序集合，按时间排序）
                    self.redis_client.zadd(COMPLETED_TASKS_KEY, {task_id: self._get_current_timestamp()})

                    # 限制已完成任务数量
                    completed_count = self.redis_client.zcard(COMPLETED_TASKS_KEY)
                    if completed_count > MAX_COMPLETED_TASKS:
                        # 删除最旧的任务
                        oldest_tasks = self.redis_client.zrange(COMPLETED_TASKS_KEY, 0, completed_count - MAX_COMPLETED_TASKS - 1)
                        if oldest_tasks:
                            # 从已完成任务集合中移除
                            self.redis_client.zrem(COMPLETED_TASKS_KEY, *oldest_tasks)

                            # 删除任务详情
                            for old_task_id in oldest_tasks:
                                self.redis_client.delete(f"{TASK_DETAILS_KEY_PREFIX}{old_task_id}")

                logger.info(f"任务状态已更新: {task_id} -> {status}")

        except Exception as e:
            logger.error(f"更新任务状态时出错: {str(e)}", exc_info=True)

    def get_active_tasks(self) -> List[Dict[str, Any]]:
        """
        获取活跃任务列表（包含自动发现RQ任务）

        Returns:
            活跃任务列表
        """
        try:
            # 自动同步RQ任务
            self._auto_sync_rq_tasks()

            # 获取活跃任务ID
            active_task_ids = self.redis_client.smembers(ACTIVE_TASKS_KEY)

            # 获取任务详情
            active_tasks = []
            for task_id in active_task_ids:
                task_details = self._get_task_details(task_id)
                if task_details:
                    # 更新任务状态
                    self._update_task_status_from_rq(task_id, task_details)
                    active_tasks.append(task_details)

            return active_tasks

        except Exception as e:
            logger.error(f"获取活跃任务列表时出错: {str(e)}", exc_info=True)
            return []

    def get_completed_tasks(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取已完成任务列表

        Args:
            limit: 限制返回的任务数量

        Returns:
            已完成任务列表
        """
        try:
            # 获取已完成任务ID（按时间倒序）
            completed_task_ids = self.redis_client.zrevrange(COMPLETED_TASKS_KEY, 0, limit - 1)

            # 获取任务详情
            completed_tasks = []
            for task_id in completed_task_ids:
                task_details = self._get_task_details(task_id)
                if task_details:
                    completed_tasks.append(task_details)

            return completed_tasks

        except Exception as e:
            logger.error(f"获取已完成任务列表时出错: {str(e)}", exc_info=True)
            return []

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            任务状态信息
        """
        try:
            # 获取任务详情
            task_details = self._get_task_details(task_id)

            if task_details:
                # 更新任务状态
                self._update_task_status_from_rq(task_id, task_details)
                return task_details

            return None

        except Exception as e:
            logger.error(f"获取任务状态时出错: {str(e)}", exc_info=True)
            return None

    def _get_task_details(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务详情

        Args:
            task_id: 任务ID

        Returns:
            任务详情
        """
        task_details_json = self.redis_client.get(f"{TASK_DETAILS_KEY_PREFIX}{task_id}")

        if task_details_json:
            return json.loads(task_details_json)

        return None

    def _update_task_status_from_rq(self, task_id: str, task_details: Dict[str, Any]) -> None:
        """
        从RQ更新任务状态

        Args:
            task_id: 任务ID
            task_details: 任务详情
        """
        try:
            # 获取RQ任务状态
            job = get_job(task_id)

            if job is None:
                # 如果任务不存在，可能已经完成或被删除
                return

            # 获取任务状态
            status = job.get_status()

            # 将RQ状态映射到我们的状态
            status_mapping = {
                "queued": "PENDING",
                "started": "STARTED",
                "finished": "SUCCESS",
                "failed": "FAILURE",
                "deferred": "PENDING",
                "scheduled": "PENDING",
                "canceled": "REVOKED"
            }

            mapped_status = status_mapping.get(status, status.upper())

            # 如果状态有变化，更新Redis中的状态
            if mapped_status != task_details.get("status"):
                result = None
                error = None

                if status == "finished":
                    result = job.result
                elif status == "failed":
                    error = str(job.exc_info)

                self.update_task_status(task_id, mapped_status, result, error)
        except Exception as e:
            logger.error(f"从RQ更新任务状态时出错: {str(e)}", exc_info=True)

    def _get_current_timestamp(self) -> int:
        """
        获取当前时间戳

        Returns:
            当前时间戳
        """
        import time
        return int(time.time())

    def _auto_sync_rq_tasks(self) -> None:
        """
        自动同步RQ任务到任务监控器
        """
        try:
            # 检查上次同步时间，避免频繁同步
            last_sync_key = "task_monitor:last_sync"
            last_sync = self.redis_client.get(last_sync_key)
            current_time = self._get_current_timestamp()

            # 如果距离上次同步不到30秒，跳过
            if last_sync and (current_time - int(last_sync)) < 30:
                return

            # 查找RQ任务键
            rq_job_keys = self.redis_client.keys("rq:job:*")

            synced_count = 0
            for job_key in rq_job_keys:
                try:
                    # 安全处理键名编码
                    if isinstance(job_key, bytes):
                        job_key_str = job_key.decode('utf-8', errors='ignore')
                    else:
                        job_key_str = str(job_key)

                    # 提取任务ID
                    task_id = job_key_str.split(":")[-1]

                    # 检查是否已存在于监控器
                    monitor_key = f"{TASK_DETAILS_KEY_PREFIX}{task_id}"
                    if self.redis_client.exists(monitor_key):
                        continue

                    # 获取RQ任务数据
                    try:
                        job_data = self.redis_client.hgetall(job_key_str)
                        if not job_data:
                            continue

                        # 创建任务详情（Redis连接已配置自动解码）
                        status = job_data.get('status', 'unknown')
                        func_name = job_data.get('description', 'unknown_task')
                        created_at = job_data.get('created_at', str(current_time))

                    except Exception as decode_error:
                        logger.debug(f"获取任务数据失败 {job_key_str}: {decode_error}")
                        continue

                    # 状态映射
                    status_mapping = {
                        'queued': 'PENDING',
                        'started': 'STARTED',
                        'finished': 'SUCCESS',
                        'failed': 'FAILURE',
                        'deferred': 'PENDING',
                        'scheduled': 'PENDING',
                        'canceled': 'REVOKED'
                    }

                    mapped_status = status_mapping.get(status, status.upper())

                    task_details = {
                        "task_id": task_id,
                        "task_name": func_name,
                        "status": mapped_status,
                        "created_at": int(float(created_at)) if created_at.replace('.', '').isdigit() else current_time,
                        "auto_synced": True  # 标记为自动同步的任务
                    }

                    # 保存任务详情
                    self.redis_client.set(
                        monitor_key,
                        json.dumps(task_details),
                        ex=86400  # 24小时过期
                    )

                    # 添加到相应的集合
                    if mapped_status in ["SUCCESS", "FAILURE", "REVOKED"]:
                        self.redis_client.zadd(COMPLETED_TASKS_KEY, {task_id: task_details["created_at"]})
                    else:
                        self.redis_client.sadd(ACTIVE_TASKS_KEY, task_id)

                    synced_count += 1

                except Exception as e:
                    logger.debug(f"同步RQ任务失败 {job_key}: {str(e)}")
                    continue

            # 更新同步时间
            self.redis_client.set(last_sync_key, current_time, ex=3600)

            if synced_count > 0:
                logger.info(f"自动同步了 {synced_count} 个RQ任务到任务监控器")

        except Exception as e:
            logger.debug(f"自动同步RQ任务时出错: {str(e)}")
            # 不抛出异常，避免影响正常功能

# 创建全局任务监控实例
task_monitor = TaskMonitor()

def get_task_monitor() -> TaskMonitor:
    """
    获取任务监控实例

    Returns:
        任务监控实例
    """
    return task_monitor
