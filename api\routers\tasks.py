"""
任务路由模块
提供与后台任务相关的API端点
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel
from typing import Dict, Any, Optional, List

from ..core.debug import get_app_logger
from ..tasks.sync_tasks import sync_materials, process_material_data
from ..core.task_manager import submit_task, get_task_status as get_task_status_tm, cancel_task, get_tasks_by_status
from ..core.task_monitor import get_task_monitor

# 获取日志记录器
logger = get_app_logger("tasks_router")

# 创建路由
router = APIRouter(
    prefix="/tasks",
    tags=["tasks"],
    responses={404: {"description": "Not found"}},
)

# 请求模型
class TaskRequest(BaseModel):
    """任务请求模型"""
    task_type: str
    params: Optional[Dict[str, Any]] = None

# 响应模型
class TaskResponse(BaseModel):
    """任务响应模型"""
    task_id: str
    status: str
    message: str

@router.post("/run", response_model=TaskResponse)
async def run_task(request: TaskRequest):
    """
    运行后台任务

    Args:
        request: 任务请求

    Returns:
        任务响应
    """
    logger.info(f"收到任务请求: {request.task_type}")

    try:
        # 根据任务类型执行不同的任务
        if request.task_type == "sync_materials":
            # 使用任务管理器提交材料同步任务
            task_id = submit_task(
                sync_materials,
                queue="default",
                timeout=1800,
                description="材料数据同步任务"
            )

            return TaskResponse(
                task_id=task_id,
                status="queued",
                message="材料同步任务已提交"
            )

        elif request.task_type == "process_material":
            # 检查必要参数
            if not request.params or "material_id" not in request.params:
                raise HTTPException(status_code=400, detail="缺少必要参数: material_id")

            # 使用任务管理器提交材料处理任务
            material_id = request.params["material_id"]
            task_id = submit_task(
                process_material_data,
                material_id,
                queue="default",
                timeout=600,
                description=f"处理材料数据 (ID: {material_id})"
            )

            return TaskResponse(
                task_id=task_id,
                status="queued",
                message=f"材料处理任务已提交 (材料ID: {material_id})"
            )

        else:
            # 未知任务类型
            raise HTTPException(status_code=400, detail=f"未知任务类型: {request.task_type}")

    except Exception as e:
        logger.error(f"提交任务时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"提交任务时出错: {str(e)}")

@router.get("/status/{task_id}", response_model=Dict[str, Any])
async def get_task_status_endpoint(task_id: str):
    """
    获取任务状态

    Args:
        task_id: 任务ID

    Returns:
        任务状态信息
    """
    logger.info(f"查询任务状态: {task_id}")

    try:
        # 使用任务管理器获取任务状态
        task_status = get_task_status_tm(task_id)
        return task_status

    except Exception as e:
        logger.error(f"获取任务状态时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取任务状态时出错: {str(e)}")

@router.get("/active", response_model=List[Dict[str, Any]])
async def get_active_tasks():
    """
    获取活跃任务列表

    Returns:
        活跃任务列表
    """
    logger.info("获取活跃任务列表")

    try:
        # 使用任务管理器获取活跃任务
        active_tasks = []

        # 获取不同状态的任务
        queued_tasks = get_tasks_by_status("queued", limit=50)
        started_tasks = get_tasks_by_status("started", limit=50)

        active_tasks.extend(queued_tasks)
        active_tasks.extend(started_tasks)

        return active_tasks

    except Exception as e:
        logger.error(f"获取活跃任务列表时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取活跃任务列表时出错: {str(e)}")

@router.get("/completed", response_model=List[Dict[str, Any]])
async def get_completed_tasks(limit: int = 10):
    """
    获取已完成任务列表

    Args:
        limit: 限制返回的任务数量

    Returns:
        已完成任务列表
    """
    logger.info(f"获取已完成任务列表 (limit={limit})")

    try:
        # 使用任务管理器获取已完成任务
        completed_tasks = []

        # 获取不同状态的已完成任务
        finished_tasks = get_tasks_by_status("finished", limit=limit//2)
        failed_tasks = get_tasks_by_status("failed", limit=limit//2)

        completed_tasks.extend(finished_tasks)
        completed_tasks.extend(failed_tasks)

        return completed_tasks[:limit]

    except Exception as e:
        logger.error(f"获取已完成任务列表时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取已完成任务列表时出错: {str(e)}")

@router.post("/cancel/{task_id}", response_model=Dict[str, Any])
async def cancel_task_endpoint(task_id: str):
    """
    取消任务

    Args:
        task_id: 任务ID

    Returns:
        取消结果
    """
    logger.info(f"取消任务: {task_id}")

    try:
        # 使用任务管理器取消任务
        result = cancel_task(task_id)

        if result.get("success"):
            return {
                "status": "success",
                "message": result.get("message", f"任务 {task_id} 已取消")
            }
        else:
            raise HTTPException(status_code=400, detail=result.get("message", "取消任务失败"))

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"取消任务时出错: {str(e)}")
