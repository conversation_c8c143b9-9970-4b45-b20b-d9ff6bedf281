"""
任务管理器
封装任务的基础操作方法，提供统一的任务管理接口
"""

import time
from typing import Dict, Any, Optional, List, Union, Callable
from datetime import datetime, timedelta
from rq.job import Job
from rq.exceptions import NoSuchJobError

from .rq_app import get_flask_rq, get_queue, get_job, job as job_decorator
from .debug import get_app_logger

# 获取日志记录器
logger = get_app_logger("task_manager")

class TaskManager:
    """任务管理器类"""

    def __init__(self):
        """初始化任务管理器"""
        self.rq = get_flask_rq()
        self.logger = logger

    def submit_task(self, func: Callable, *args, queue: str = "default",
                   timeout: int = 300, result_ttl: int = 3600,
                   description: str = None, **kwargs) -> str:
        """
        提交任务到队列

        Args:
            func: 任务函数
            *args: 任务参数
            queue: 队列名称 (default, high, low)
            timeout: 任务超时时间（秒）
            result_ttl: 结果保留时间（秒）
            description: 任务描述
            **kwargs: 其他关键字参数

        Returns:
            任务ID
        """
        try:
            # 构建任务元数据
            meta = kwargs.pop('meta', {})
            if description:
                meta['description'] = description
                meta['submitted_at'] = datetime.now().isoformat()

            # 获取队列并提交任务
            task_queue = get_queue(queue)
            job = task_queue.enqueue(
                func, *args,
                timeout=timeout,
                result_ttl=result_ttl,
                meta=meta,
                **kwargs
            )

            self.logger.info(f"任务已提交到队列 {queue}，任务ID: {job.id}")
            return job.id

        except Exception as e:
            self.logger.error(f"提交任务失败: {e}", exc_info=True)
            raise

    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            任务状态信息字典
        """
        try:
            # 使用安全的方式获取任务
            try:
                job = get_job(task_id)
            except UnicodeDecodeError as decode_error:
                self.logger.warning(f"任务 {task_id} 存在编码问题，跳过: {decode_error}")
                return {
                    "id": task_id,
                    "status": "encoding_error",
                    "message": "任务数据编码错误"
                }

            if job is None:
                return {
                    "id": task_id,
                    "status": "not_found",
                    "message": "任务不存在"
                }

            status_info = {
                "id": job.id,
                "status": job.get_status().value,
                "description": job.meta.get('description', ''),
                "queue": job.origin,
                "created_at": job.created_at.isoformat() if job.created_at else None,
                "started_at": job.started_at.isoformat() if job.started_at else None,
                "ended_at": job.ended_at.isoformat() if job.ended_at else None,
                "timeout": job.timeout,
                "result_ttl": job.result_ttl,
                "meta": job.meta
            }

            # 根据状态添加额外信息
            if job.is_finished:
                status_info["result"] = job.result
                status_info["duration"] = self._calculate_duration(job)
            elif job.is_failed:
                status_info["error"] = str(job.exc_info) if job.exc_info else "未知错误"
                status_info["failure_reason"] = job.meta.get('failure_reason', '')
            elif job.is_started:
                status_info["progress"] = job.meta.get('progress', 0)

            return status_info

        except NoSuchJobError:
            return {
                "id": task_id,
                "status": "not_found",
                "message": "任务不存在"
            }
        except Exception as e:
            self.logger.error(f"获取任务状态失败: {e}", exc_info=True)
            return {
                "id": task_id,
                "status": "error",
                "message": str(e)
            }

    def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """
        取消任务

        Args:
            task_id: 任务ID

        Returns:
            取消结果
        """
        try:
            job = get_job(task_id)
            if job is None:
                return {
                    "success": False,
                    "message": "任务不存在"
                }

            # 检查任务状态
            if job.is_finished:
                return {
                    "success": False,
                    "message": "任务已完成，无法取消"
                }
            elif job.is_failed:
                return {
                    "success": False,
                    "message": "任务已失败，无法取消"
                }

            # 取消任务
            job.cancel()
            self.logger.info(f"任务 {task_id} 已取消")

            return {
                "success": True,
                "message": f"任务 {task_id} 已成功取消"
            }

        except Exception as e:
            self.logger.error(f"取消任务失败: {e}", exc_info=True)
            return {
                "success": False,
                "message": str(e)
            }

    def retry_task(self, task_id: str) -> Dict[str, Any]:
        """
        重试失败的任务

        Args:
            task_id: 任务ID

        Returns:
            重试结果
        """
        try:
            job = get_job(task_id)
            if job is None:
                return {
                    "success": False,
                    "message": "任务不存在"
                }

            if not job.is_failed:
                return {
                    "success": False,
                    "message": "只能重试失败的任务"
                }

            # 重新提交任务
            new_job = job.retry()
            self.logger.info(f"任务 {task_id} 已重试，新任务ID: {new_job.id}")

            return {
                "success": True,
                "message": f"任务已重试",
                "new_task_id": new_job.id
            }

        except Exception as e:
            self.logger.error(f"重试任务失败: {e}", exc_info=True)
            return {
                "success": False,
                "message": str(e)
            }

    def get_queue_info(self, queue_name: str = None) -> Dict[str, Any]:
        """
        获取队列信息

        Args:
            queue_name: 队列名称，为None时返回所有队列信息

        Returns:
            队列信息字典
        """
        try:
            if queue_name:
                queue = get_queue(queue_name)
                return {
                    queue_name: self._get_single_queue_info(queue)
                }
            else:
                # 返回所有队列信息
                queues_info = {}
                for name in ['default', 'high', 'low']:
                    queue = get_queue(name)
                    queues_info[name] = self._get_single_queue_info(queue)
                return queues_info

        except Exception as e:
            self.logger.error(f"获取队列信息失败: {e}", exc_info=True)
            return {"error": str(e)}

    def _get_single_queue_info(self, queue) -> Dict[str, Any]:
        """获取单个队列的信息"""
        return {
            "name": queue.name,
            "length": len(queue),
            "is_empty": queue.is_empty(),
            "job_ids": queue.job_ids[:10],  # 只返回前10个任务ID
            "started_job_registry": len(queue.started_job_registry),
            "finished_job_registry": len(queue.finished_job_registry),
            "failed_job_registry": len(queue.failed_job_registry),
            "deferred_job_registry": len(queue.deferred_job_registry)
        }

    def get_tasks_by_status(self, status: str, queue_name: str = "default",
                           limit: int = 50) -> List[Dict[str, Any]]:
        """
        根据状态获取任务列表

        Args:
            status: 任务状态 (queued, started, finished, failed, deferred)
            queue_name: 队列名称
            limit: 返回任务数量限制

        Returns:
            任务列表
        """
        try:
            queue = get_queue(queue_name)
            tasks = []

            if status == "queued":
                job_ids = queue.job_ids[:limit]
            elif status == "started":
                job_ids = queue.started_job_registry.get_job_ids()[:limit]
            elif status == "finished":
                job_ids = queue.finished_job_registry.get_job_ids()[:limit]
            elif status == "failed":
                job_ids = queue.failed_job_registry.get_job_ids()[:limit]
            elif status == "deferred":
                job_ids = queue.deferred_job_registry.get_job_ids()[:limit]
            else:
                return []

            for job_id in job_ids:
                task_info = self.get_task_status(job_id)
                if task_info.get("status") != "not_found":
                    tasks.append(task_info)

            return tasks

        except Exception as e:
            self.logger.error(f"获取任务列表失败: {e}", exc_info=True)
            return []

    def clear_queue(self, queue_name: str, status: str = "all") -> Dict[str, Any]:
        """
        清空队列

        Args:
            queue_name: 队列名称
            status: 要清空的任务状态 (all, queued, finished, failed)

        Returns:
            清空结果
        """
        try:
            queue = get_queue(queue_name)
            cleared_count = 0

            if status in ["all", "queued"]:
                cleared_count += len(queue)
                queue.empty()

            if status in ["all", "finished"]:
                finished_count = len(queue.finished_job_registry)
                queue.finished_job_registry.clear()
                cleared_count += finished_count

            if status in ["all", "failed"]:
                failed_count = len(queue.failed_job_registry)
                queue.failed_job_registry.clear()
                cleared_count += failed_count

            self.logger.info(f"队列 {queue_name} 已清空 {cleared_count} 个任务")

            return {
                "success": True,
                "message": f"已清空 {cleared_count} 个任务",
                "cleared_count": cleared_count
            }

        except Exception as e:
            self.logger.error(f"清空队列失败: {e}", exc_info=True)
            return {
                "success": False,
                "message": str(e)
            }

    def _calculate_duration(self, job: Job) -> Optional[float]:
        """计算任务执行时长"""
        if job.started_at and job.ended_at:
            return (job.ended_at - job.started_at).total_seconds()
        return None

    def create_job_decorator(self, queue: str = "default", timeout: int = 300,
                           result_ttl: int = 3600, description: str = None):
        """
        创建任务装饰器

        Args:
            queue: 队列名称
            timeout: 超时时间
            result_ttl: 结果保留时间
            description: 任务描述

        Returns:
            装饰器函数
        """
        return job_decorator(
            queue=queue,
            timeout=timeout,
            result_ttl=result_ttl,
            description=description
        )

# 创建全局任务管理器实例
task_manager = TaskManager()

# 便捷函数
def submit_task(*args, **kwargs) -> str:
    """提交任务的便捷函数"""
    return task_manager.submit_task(*args, **kwargs)

def get_task_status(task_id: str) -> Dict[str, Any]:
    """获取任务状态的便捷函数"""
    return task_manager.get_task_status(task_id)

def cancel_task(task_id: str) -> Dict[str, Any]:
    """取消任务的便捷函数"""
    return task_manager.cancel_task(task_id)

def retry_task(task_id: str) -> Dict[str, Any]:
    """重试任务的便捷函数"""
    return task_manager.retry_task(task_id)

def get_queue_info(queue_name: str = None) -> Dict[str, Any]:
    """获取队列信息的便捷函数"""
    return task_manager.get_queue_info(queue_name)

def get_tasks_by_status(status: str, queue_name: str = "default", limit: int = 50) -> List[Dict[str, Any]]:
    """根据状态获取任务列表的便捷函数"""
    return task_manager.get_tasks_by_status(status, queue_name, limit)

def clear_queue(queue_name: str, status: str = "all") -> Dict[str, Any]:
    """清空队列的便捷函数"""
    return task_manager.clear_queue(queue_name, status)

def job(queue: str = "default", timeout: int = 300, result_ttl: int = 3600, description: str = None):
    """任务装饰器的便捷函数"""
    return task_manager.create_job_decorator(queue, timeout, result_ttl, description)
