#!/usr/bin/env python
"""
测试RQ Dashboard集成
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_rq_dashboard_integration():
    """测试RQ Dashboard集成"""
    print("🔧 测试RQ Dashboard集成...\n")
    
    # 测试导入
    try:
        from api.core.rq_dashboard_integration import (
            get_rq_dashboard_integration, 
            create_rq_dashboard_wsgi, 
            is_rq_dashboard_available,
            test_rq_dashboard
        )
        print("✅ RQ Dashboard集成模块导入成功")
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    # 测试WSGI适配器
    try:
        from api.core.wsgi_adapter import create_simple_wsgi_adapter, create_wsgi_adapter
        print("✅ WSGI适配器模块导入成功")
    except Exception as e:
        print(f"❌ WSGI适配器导入失败: {e}")
        return False
    
    # 测试RQ Dashboard创建
    print("\n🔍 测试RQ Dashboard创建...")
    success = test_rq_dashboard()
    
    if success:
        print("\n🔍 测试WSGI应用创建...")
        try:
            wsgi_app = create_rq_dashboard_wsgi()
            print("✅ WSGI应用创建成功")
            
            # 测试适配器
            asgi_app = create_simple_wsgi_adapter(wsgi_app)
            print("✅ ASGI适配器创建成功")
            
        except Exception as e:
            print(f"❌ WSGI/ASGI创建失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    # 检查可用性
    available = is_rq_dashboard_available()
    print(f"\n📊 RQ Dashboard可用性: {'✅ 可用' if available else '❌ 不可用'}")
    
    return success and available

def test_fastapi_integration():
    """测试FastAPI集成"""
    print("\n🚀 测试FastAPI集成...")
    
    try:
        # 导入主应用（这会触发集成逻辑）
        from api.main import app
        print("✅ FastAPI应用导入成功")
        
        # 检查路由
        routes = [route.path for route in app.routes]
        rq_mounted = any("/rq" in route for route in routes)
        
        if rq_mounted:
            print("✅ RQ Dashboard已挂载到 /rq 路径")
        else:
            print("⚠️ RQ Dashboard未挂载（可能是因为不可用）")
        
        return True
        
    except Exception as e:
        print(f"❌ FastAPI集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 RQ Dashboard集成测试开始...\n")
    
    # 测试集成模块
    integration_ok = test_rq_dashboard_integration()
    
    # 测试FastAPI集成
    fastapi_ok = test_fastapi_integration()
    
    # 总结
    print("\n" + "="*50)
    print("📋 测试结果总结:")
    print(f"  RQ Dashboard集成: {'✅ 正常' if integration_ok else '❌ 失败'}")
    print(f"  FastAPI集成: {'✅ 正常' if fastapi_ok else '❌ 失败'}")
    
    if integration_ok and fastapi_ok:
        print("\n🎉 所有测试通过！")
        print("🌐 启动应用后可访问: http://localhost:8000/rq")
        print("📝 启动命令: uvicorn api.main:app --reload")
    else:
        print("\n⚠️  部分测试失败，请检查错误信息。")
        if not integration_ok:
            print("💡 提示: 请确保已安装 rq-dashboard: pip install rq-dashboard")

if __name__ == "__main__":
    main()
