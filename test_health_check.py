#!/usr/bin/env python
"""
测试健康检查
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_health_check():
    """测试健康检查"""
    print("🔍 测试健康检查路由...\n")
    
    try:
        from fastapi.testclient import TestClient
        from api.main import app
        
        client = TestClient(app)
        
        # 测试健康检查
        print("1. 测试健康检查...")
        response = client.get("/health")
        print(f"   GET /health -> {response.status_code}")
        
        if response.status_code == 200:
            print(f"   响应内容: {response.json()}")
            print("   ✅ 健康检查正常")
        else:
            print(f"   ❌ 健康检查失败: {response.status_code}")
            if response.content:
                print(f"   响应内容: {response.content}")
        
        # 测试其他路径
        print("\n2. 测试其他路径...")
        
        # 测试docs
        response = client.get("/docs")
        print(f"   GET /docs -> {response.status_code}")
        
        # 测试openapi.json
        response = client.get("/openapi.json")
        print(f"   GET /openapi.json -> {response.status_code}")
        
        # 测试一个API路径
        response = client.get("/api/unified-tasks/info")
        print(f"   GET /api/unified-tasks/info -> {response.status_code}")
        
        # 测试RQ路径
        response = client.get("/rq")
        print(f"   GET /rq -> {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_health_check()
