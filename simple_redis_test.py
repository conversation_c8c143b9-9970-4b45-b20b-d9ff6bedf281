#!/usr/bin/env python
"""
简单的Redis连接测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_redis():
    """测试Redis连接"""
    try:
        from api.core.rq_app import get_redis_connection, get_redis_connection_for_monitor
        
        print("测试RQ Redis连接...")
        rq_redis = get_redis_connection()
        rq_redis.ping()
        print("✅ RQ Redis连接正常")
        
        print("测试监控Redis连接...")
        monitor_redis = get_redis_connection_for_monitor()
        monitor_redis.ping()
        print("✅ 监控Redis连接正常")
        
        # 测试基本操作
        test_key = "test:fix"
        test_value = "测试"
        monitor_redis.set(test_key, test_value)
        result = monitor_redis.get(test_key)
        print(f"✅ 测试操作成功: {result}")
        monitor_redis.delete(test_key)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始Redis连接测试...")
    success = test_redis()
    if success:
        print("🎉 测试通过！")
    else:
        print("❌ 测试失败！")
