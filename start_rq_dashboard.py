#!/usr/bin/env python
"""
RQ Dashboard启动脚本
用于启动RQ Dashboard监控界面
"""

import os
import sys

def start_rq_dashboard():
    """启动RQ Dashboard"""
    try:
        print("正在启动RQ Dashboard...")

        # 导入rq_dashboard
        from rq_dashboard import app

        # 配置RQ Dashboard
        app.config.from_object('rq_dashboard.default_settings')

        # 设置Redis连接
        redis_url = 'redis://:yhb25IEz@***********:6379/3'
        app.config['RQ_DASHBOARD_REDIS_URL'] = redis_url

        # 设置监听地址和端口
        host = '0.0.0.0'
        port = 9181

        print(f"✅ RQ Dashboard配置完成")
        print(f"📡 Redis URL: {redis_url}")
        print(f"🌐 访问地址: http://localhost:{port}")
        print(f"🛑 按Ctrl+C停止服务")
        print("-" * 50)

        # 启动Flask应用
        app.run(host=host, port=port, debug=False, use_reloader=False)

    except ImportError as e:
        print(f"❌ 导入rq_dashboard失败: {e}")
        print("请安装rq-dashboard: pip install rq-dashboard")
        return False
    except Exception as e:
        print(f"❌ 启动RQ Dashboard失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    start_rq_dashboard()
