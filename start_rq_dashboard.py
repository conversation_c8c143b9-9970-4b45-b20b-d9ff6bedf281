#!/usr/bin/env python
"""
RQ Dashboard启动脚本
用于启动RQ Dashboard监控界面
"""

import os
import sys
import subprocess

def start_original_rq_dashboard():
    """启动原始的RQ Dashboard"""
    try:
        print("正在启动原始RQ Dashboard...")

        # 导入rq_dashboard
        from rq_dashboard import app

        # 配置RQ Dashboard
        app.config.from_object('rq_dashboard.default_settings')

        # 设置Redis连接
        redis_url = 'redis://:yhb25IEz@***********:6379/3'
        app.config['RQ_DASHBOARD_REDIS_URL'] = [redis_url]  # 使用列表格式

        # 设置监听地址和端口
        host = '0.0.0.0'
        port = 9181

        print(f"✅ RQ Dashboard配置完成")
        print(f"📡 Redis URL: {redis_url}")
        print(f"🌐 访问地址: http://localhost:{port}")
        print(f"🛑 按Ctrl+C停止服务")
        print("-" * 50)

        # 启动Flask应用
        app.run(host=host, port=port, debug=False, use_reloader=False)

    except ImportError as e:
        print(f"❌ 导入rq_dashboard失败: {e}")
        print("请安装rq-dashboard: pip install rq-dashboard")
        return False
    except Exception as e:
        print(f"❌ 启动RQ Dashboard失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def start_standalone_dashboard():
    """启动独立的RQ Dashboard服务"""
    print("🚀 启动独立的RQ Dashboard服务...")

    try:
        # 启动独立服务
        cmd = [sys.executable, "-m", "uvicorn", "standalone_rq_dashboard:app", "--host", "0.0.0.0", "--port", "9181"]

        print(f"执行命令: {' '.join(cmd)}")
        process = subprocess.Popen(cmd, cwd=os.getcwd())

        print("✅ RQ Dashboard服务已启动")
        print("🌐 访问地址: http://localhost:9181")
        print("⏹️  按 Ctrl+C 停止服务")

        # 等待进程
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止服务...")
            process.terminate()
            process.wait()
            print("✅ 服务已停止")

    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔧 RQ Dashboard启动器\n")

    print("请选择启动方式:")
    print("1. 原始RQ Dashboard (Flask)")
    print("2. 独立FastAPI服务")
    print("3. 退出")

    try:
        choice = input("\n请输入选择 (1-3): ").strip()

        if choice == "1":
            start_original_rq_dashboard()
        elif choice == "2":
            start_standalone_dashboard()
        elif choice == "3":
            print("👋 再见！")
        else:
            print("❌ 无效选择")

    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"❌ 出错: {e}")

if __name__ == '__main__':
    main()
