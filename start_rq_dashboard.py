#!/usr/bin/env python
"""
RQ Dashboard启动脚本
用于启动RQ Dashboard监控界面
"""

import os
import sys

def start_rq_dashboard():
    """启动RQ Dashboard"""
    try:
        print("正在启动RQ Dashboard...")

        # 尝试不同的导入方式
        try:
            # 方式1：直接导入app
            from rq_dashboard import app
            print("✅ 使用方式1导入成功")
        except ImportError:
            try:
                # 方式2：从web模块导入
                from rq_dashboard.web import app
                print("✅ 使用方式2导入成功")
            except ImportError:
                try:
                    # 方式3：创建app
                    import rq_dashboard
                    from flask import Flask
                    app = Flask(__name__)
                    # 使用rq_dashboard的蓝图
                    if hasattr(rq_dashboard, 'blueprint'):
                        app.register_blueprint(rq_dashboard.blueprint)
                    print("✅ 使用方式3创建成功")
                except Exception as e3:
                    print(f"❌ 所有导入方式都失败: {e3}")
                    return False

        # 设置Redis连接
        redis_url = 'redis://:yhb25IEz@***********:6379/3'

        # 配置app
        app.config['RQ_DASHBOARD_REDIS_URL'] = redis_url
        app.config['SECRET_KEY'] = 'rq-dashboard-secret-key'

        # 设置监听地址和端口
        host = '0.0.0.0'
        port = 9181

        print(f"✅ RQ Dashboard配置完成")
        print(f"📡 Redis URL: {redis_url}")
        print(f"🌐 访问地址: http://localhost:{port}")
        print(f"🛑 按Ctrl+C停止服务")
        print("-" * 50)

        # 启动Flask应用
        app.run(host=host, port=port, debug=False, use_reloader=False)

    except ImportError as e:
        print(f"❌ 导入rq_dashboard失败: {e}")
        print("请安装rq-dashboard: pip install rq-dashboard")
        return False
    except Exception as e:
        print(f"❌ 启动RQ Dashboard失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    start_rq_dashboard()
