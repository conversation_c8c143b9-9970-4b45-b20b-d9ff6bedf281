"""
RQ应用配置模块
用于配置和初始化RQ队列 - 使用Flask-RQ2
"""

import redis
from flask_rq2 import RQ
from rq.job import Job
from rq_scheduler import Scheduler
from typing import Optional

from ..config import REDIS_HOST, REDIS_PORT, REDIS_PASSWORD, REDIS_DB, REDIS_URL

# 创建Flask-RQ2实例 - 使用直接配置方式
from flask import Flask

# 创建一个临时Flask应用用于配置RQ
temp_app = Flask(__name__)
temp_app.config['RQ_REDIS_URL'] = REDIS_URL
temp_app.config['RQ_QUEUES'] = ['default', 'high', 'low']
temp_app.config['RQ_DEFAULT_TIMEOUT'] = 300

# 创建RQ实例并初始化
rq = RQ()
rq.init_app(temp_app)

# 创建Redis连接（用于兼容性）
def get_redis_connection():
    """获取Redis连接"""
    return redis.Redis(
        host=REDIS_HOST,
        port=int(REDIS_PORT),
        password=REDIS_PASSWORD,
        db=int(REDIS_DB),
        decode_responses=False,  # 关闭自动解码，让RQ处理
        encoding='utf-8',
        encoding_errors='ignore'  # 忽略编码错误
    )

# 创建专门用于任务监控的Redis连接
def get_redis_connection_for_monitor():
    """获取用于任务监控的Redis连接"""
    return redis.Redis(
        host=REDIS_HOST,
        port=int(REDIS_PORT),
        password=REDIS_PASSWORD,
        db=int(REDIS_DB),
        decode_responses=True,  # 启用自动解码，用于监控
        encoding='utf-8',
        encoding_errors='replace'  # 替换无法解码的字符
    )

# 获取队列（使用Flask-RQ2方式）
default_queue = rq.get_queue('default')
high_queue = rq.get_queue('high')
low_queue = rq.get_queue('low')

# 创建调度器
scheduler = Scheduler(connection=get_redis_connection(), queue=default_queue)

def get_rq_instance():
    """获取RQ实例（兼容旧代码）"""
    # 返回Flask-RQ2实例
    return rq

def get_queue(queue_name: str = "default"):
    """
    获取指定名称的队列

    Args:
        queue_name: 队列名称

    Returns:
        队列实例
    """
    return rq.get_queue(queue_name)

def get_scheduler() -> Scheduler:
    """
    获取调度器实例

    Returns:
        调度器实例
    """
    return scheduler

def get_job(job_id: str) -> Optional[Job]:
    """
    获取任务实例

    Args:
        job_id: 任务ID

    Returns:
        任务实例
    """
    try:
        return Job.fetch(job_id, connection=get_redis_connection())
    except UnicodeDecodeError:
        # 如果遇到编码错误，返回None
        return None
    except Exception:
        # 其他错误也返回None，让调用者处理
        return None

def enqueue_job(func, *args, **kwargs) -> Job:
    """
    将任务加入队列

    Args:
        func: 任务函数
        *args: 位置参数
        **kwargs: 关键字参数

    Returns:
        任务实例
    """
    queue_name = kwargs.pop("queue", "default")
    return rq.get_queue(queue_name).enqueue(func, *args, **kwargs)

def schedule_job(func, *args, **kwargs) -> Job:
    """
    调度定时任务

    Args:
        func: 任务函数
        *args: 位置参数
        **kwargs: 关键字参数

    Returns:
        任务实例
    """
    # 从kwargs中提取调度参数
    interval = kwargs.pop("interval", None)
    cron_string = kwargs.pop("cron_string", None)
    queue_name = kwargs.pop("queue", "default")

    # 获取调度器
    scheduler = get_scheduler()

    if interval is not None:
        # 按间隔调度
        return scheduler.schedule(
            scheduled_time=interval,
            func=func,
            args=args,
            kwargs=kwargs,
            queue_name=queue_name,
            repeat=kwargs.pop("repeat", None)
        )
    elif cron_string is not None:
        # 按Cron表达式调度
        return scheduler.cron(
            cron_string=cron_string,
            func=func,
            args=args,
            kwargs=kwargs,
            queue_name=queue_name
        )
    else:
        # 如果没有指定调度参数，则立即执行
        return rq.get_queue(queue_name).enqueue(func, *args, **kwargs)

# Flask-RQ2 任务装饰器
def job(queue='default', timeout=None, result_ttl=None, ttl=None,
        failure_ttl=None, depends_on=None, job_id=None, at_front=False,
        meta=None, description=None, job_timeout=None, retry=None):
    """
    Flask-RQ2 任务装饰器

    Args:
        queue: 队列名称
        timeout: 任务超时时间
        result_ttl: 结果保留时间
        ttl: 任务生存时间
        failure_ttl: 失败任务保留时间
        depends_on: 依赖的任务
        job_id: 任务ID
        at_front: 是否插入到队列前端
        meta: 元数据
        description: 任务描述
        job_timeout: 任务超时时间（别名）
        retry: 重试配置

    Returns:
        装饰器函数
    """
    # Flask-RQ2的job装饰器参数可能不同，使用简化版本
    def decorator(func):
        # 将函数注册到指定队列
        def queue_func(*args, **kwargs):
            # 构建任务元数据
            task_meta = meta or {}
            if description:
                task_meta['description'] = description
            if retry:
                task_meta['retry'] = retry

            return rq.get_queue(queue).enqueue(func, *args, timeout=timeout or job_timeout,
                                             result_ttl=result_ttl, ttl=ttl,
                                             failure_ttl=failure_ttl, depends_on=depends_on,
                                             job_id=job_id, at_front=at_front, meta=task_meta, **kwargs)

        # 添加queue方法到函数
        func.queue = queue_func
        return func

    return decorator

# 获取Flask-RQ2实例的便捷函数
def get_flask_rq():
    """
    获取Flask-RQ2实例

    Returns:
        Flask-RQ2实例
    """
    return rq
