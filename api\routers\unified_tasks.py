"""
统一任务管理API路由
提供完整的Flask-RQ任务管理功能，包括即时任务、定时任务和队列管理
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any, Optional, List
from datetime import datetime

from ..core.debug import get_app_logger
from ..core.task_manager import (
    submit_task, get_task_status, cancel_task, retry_task,
    get_queue_info, get_tasks_by_status, clear_queue, task_manager
)
from ..core.scheduler import (
    start_scheduler, stop_scheduler, schedule_task, schedule_recurring_task,
    cancel_scheduled_task, get_scheduled_jobs, setup_default_scheduled_tasks
)
from ..tasks.sync_tasks import sync_materials, process_material_data
from ..tasks.excel_material_processor import process_excel_materials

# 获取日志记录器
logger = get_app_logger("unified_tasks")

# 创建路由
router = APIRouter(
    prefix="/unified-tasks",
    tags=["unified-tasks"],
    responses={404: {"description": "Not found"}},
)

# 请求模型
class TaskSubmitRequest(BaseModel):
    """任务提交请求模型"""
    task_type: str
    args: List[Any] = []
    kwargs: Dict[str, Any] = {}
    queue: str = "default"
    timeout: int = 300
    description: str = None

class ScheduledTaskRequest(BaseModel):
    """定时任务请求模型"""
    task_type: str
    scheduled_time: datetime
    args: List[Any] = []
    kwargs: Dict[str, Any] = {}
    queue: str = "default"
    timeout: int = 300
    description: str = None

class RecurringTaskRequest(BaseModel):
    """重复任务请求模型"""
    task_type: str
    interval_seconds: int
    args: List[Any] = []
    kwargs: Dict[str, Any] = {}
    queue: str = "default"
    timeout: int = 300
    description: str = None

# 响应模型
class TaskResponse(BaseModel):
    """任务响应模型"""
    task_id: str
    status: str
    message: str

# 任务函数映射
TASK_FUNCTIONS = {
    "sync_materials": sync_materials,
    "process_material_data": process_material_data,
    "process_excel_materials": process_excel_materials,
}

# ==================== 即时任务管理 ====================

@router.post("/submit", response_model=TaskResponse)
async def submit_task_endpoint(request: TaskSubmitRequest):
    """
    提交即时任务

    Args:
        request: 任务提交请求

    Returns:
        任务响应
    """
    logger.info(f"收到任务提交请求: {request.task_type}")

    try:
        # 获取任务函数
        if request.task_type not in TASK_FUNCTIONS:
            raise HTTPException(status_code=400, detail=f"未知任务类型: {request.task_type}")

        task_func = TASK_FUNCTIONS[request.task_type]

        # 提交任务
        task_id = submit_task(
            task_func,
            *request.args,
            queue=request.queue,
            timeout=request.timeout,
            description=request.description,
            **request.kwargs
        )

        return TaskResponse(
            task_id=task_id,
            status="queued",
            message=f"任务 {request.task_type} 已提交"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提交任务失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"提交任务失败: {str(e)}")

@router.get("/status/{task_id}")
async def get_task_status_endpoint(task_id: str):
    """获取任务状态"""
    try:
        status = get_task_status(task_id)
        return status
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")

@router.post("/cancel/{task_id}")
async def cancel_task_endpoint(task_id: str):
    """取消任务"""
    try:
        result = cancel_task(task_id)
        return result
    except Exception as e:
        logger.error(f"取消任务失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")

@router.post("/retry/{task_id}")
async def retry_task_endpoint(task_id: str):
    """重试任务"""
    try:
        result = retry_task(task_id)
        return result
    except Exception as e:
        logger.error(f"重试任务失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"重试任务失败: {str(e)}")

# ==================== 定时任务管理 ====================

@router.post("/schedule", response_model=TaskResponse)
async def schedule_task_endpoint(request: ScheduledTaskRequest):
    """
    调度定时任务

    Args:
        request: 定时任务请求

    Returns:
        任务响应
    """
    logger.info(f"收到定时任务调度请求: {request.task_type}")

    try:
        # 获取任务函数
        if request.task_type not in TASK_FUNCTIONS:
            raise HTTPException(status_code=400, detail=f"未知任务类型: {request.task_type}")

        task_func = TASK_FUNCTIONS[request.task_type]

        # 调度任务
        task_id = schedule_task(
            task_func,
            request.scheduled_time,
            *request.args,
            queue=request.queue,
            timeout=request.timeout,
            description=request.description,
            **request.kwargs
        )

        return TaskResponse(
            task_id=task_id,
            status="scheduled",
            message=f"定时任务 {request.task_type} 已调度，执行时间: {request.scheduled_time}"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"调度定时任务失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"调度定时任务失败: {str(e)}")

@router.post("/schedule-recurring", response_model=TaskResponse)
async def schedule_recurring_task_endpoint(request: RecurringTaskRequest):
    """
    调度重复任务

    Args:
        request: 重复任务请求

    Returns:
        任务响应
    """
    logger.info(f"收到重复任务调度请求: {request.task_type}")

    try:
        # 获取任务函数
        if request.task_type not in TASK_FUNCTIONS:
            raise HTTPException(status_code=400, detail=f"未知任务类型: {request.task_type}")

        task_func = TASK_FUNCTIONS[request.task_type]

        # 调度重复任务
        task_id = schedule_recurring_task(
            task_func,
            request.interval_seconds,
            *request.args,
            queue=request.queue,
            timeout=request.timeout,
            description=request.description,
            **request.kwargs
        )

        return TaskResponse(
            task_id=task_id,
            status="scheduled",
            message=f"重复任务 {request.task_type} 已调度，间隔: {request.interval_seconds}秒"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"调度重复任务失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"调度重复任务失败: {str(e)}")

@router.delete("/scheduled/{task_id}")
async def cancel_scheduled_task_endpoint(task_id: str):
    """取消定时任务"""
    try:
        success = cancel_scheduled_task(task_id)
        if success:
            return {"success": True, "message": f"定时任务 {task_id} 已取消"}
        else:
            raise HTTPException(status_code=404, detail=f"定时任务 {task_id} 不存在或已执行")
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消定时任务失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"取消定时任务失败: {str(e)}")

@router.get("/scheduled")
async def get_scheduled_jobs_endpoint():
    """获取所有定时任务"""
    try:
        jobs = get_scheduled_jobs()
        return {"scheduled_jobs": jobs}
    except Exception as e:
        logger.error(f"获取定时任务列表失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取定时任务列表失败: {str(e)}")

# ==================== 队列管理 ====================

@router.get("/queues")
async def get_queue_info_endpoint(queue_name: str = None):
    """获取队列信息"""
    try:
        info = get_queue_info(queue_name)
        return info
    except Exception as e:
        logger.error(f"获取队列信息失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取队列信息失败: {str(e)}")

@router.get("/tasks/{status}")
async def get_tasks_by_status_endpoint(status: str, queue_name: str = "default", limit: int = 50):
    """根据状态获取任务列表"""
    try:
        tasks = get_tasks_by_status(status, queue_name, limit)
        return {"tasks": tasks}
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")

@router.delete("/queues/{queue_name}/clear")
async def clear_queue_endpoint(queue_name: str, status: str = "all"):
    """清空队列"""
    try:
        result = clear_queue(queue_name, status)
        return result
    except Exception as e:
        logger.error(f"清空队列失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"清空队列失败: {str(e)}")

# ==================== 调度器管理 ====================

@router.post("/scheduler/start")
async def start_scheduler_endpoint():
    """启动调度器"""
    try:
        start_scheduler()
        return {"success": True, "message": "调度器已启动"}
    except Exception as e:
        logger.error(f"启动调度器失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"启动调度器失败: {str(e)}")

@router.post("/scheduler/stop")
async def stop_scheduler_endpoint():
    """停止调度器"""
    try:
        stop_scheduler()
        return {"success": True, "message": "调度器已停止"}
    except Exception as e:
        logger.error(f"停止调度器失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"停止调度器失败: {str(e)}")

@router.post("/scheduler/setup-defaults")
async def setup_default_tasks_endpoint():
    """设置默认定时任务"""
    try:
        setup_default_scheduled_tasks()
        return {"success": True, "message": "默认定时任务已设置"}
    except Exception as e:
        logger.error(f"设置默认定时任务失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"设置默认定时任务失败: {str(e)}")

# ==================== 系统信息 ====================

@router.get("/info")
async def get_system_info():
    """获取任务系统信息"""
    try:
        return {
            "task_system": "Flask-RQ",
            "available_tasks": list(TASK_FUNCTIONS.keys()),
            "queues": ["default", "high", "low"],
            "scheduler_running": False  # 需要从调度器模块获取状态
        }
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")
