"""
RQ Dashboard集成模块
将RQ Dashboard集成到FastAPI应用中
"""

import os
from typing import Optional
from flask import Flask
from werkzeug.middleware.dispatcher import DispatcherMiddleware
from werkzeug.serving import WSGIRequestHandler

from .debug import get_app_logger
from ..config import REDIS_HOST, REDIS_PORT, REDIS_PASSWORD, REDIS_DB

# 获取日志记录器
logger = get_app_logger("rq_dashboard")

class RQDashboardIntegration:
    """RQ Dashboard集成类"""

    def __init__(self):
        self.flask_app: Optional[Flask] = None
        # 修复Redis URL格式，确保兼容RQ Dashboard
        if REDIS_PASSWORD:
            self.redis_url = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
        else:
            self.redis_url = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
        self._initialized = False

    def create_dashboard_app(self) -> Optional[Flask]:
        """创建RQ Dashboard Flask应用"""
        try:
            # 设置环境变量，让RQ Dashboard自动使用
            import os
            os.environ['RQ_DASHBOARD_REDIS_URL'] = self.redis_url

            # 使用RQ Dashboard的标准创建方式
            try:
                # 方式1：直接导入app
                from rq_dashboard import app as dashboard_app
                logger.info("✅ 使用直接导入方式创建RQ Dashboard")

            except (ImportError, AttributeError):
                try:
                    # 方式2：从web模块导入app
                    from rq_dashboard.web import app as dashboard_app
                    logger.info("✅ 使用web模块导入方式创建RQ Dashboard")

                except (ImportError, AttributeError):
                    # 方式3：手动创建
                    from flask import Flask
                    import rq_dashboard

                    dashboard_app = Flask(__name__)

                    # 注册蓝图
                    if hasattr(rq_dashboard, 'blueprint'):
                        dashboard_app.register_blueprint(rq_dashboard.blueprint)
                        logger.info("✅ 使用蓝图方式创建RQ Dashboard")
                    else:
                        logger.error("❌ 无法找到RQ Dashboard蓝图")
                        return None

            # 确保配置正确 - RQ Dashboard期望Redis URL是列表格式
            dashboard_app.config.update({
                'SECRET_KEY': 'rq-dashboard-secret-key-for-materials-system',
                'RQ_DASHBOARD_REDIS_URL': [self.redis_url],  # 必须是列表
                'REDIS_URL': self.redis_url,
            })

            # 尝试加载默认设置
            try:
                dashboard_app.config.from_object('rq_dashboard.default_settings')
                # 重新设置Redis URL确保不被覆盖 - 必须是列表格式
                dashboard_app.config['RQ_DASHBOARD_REDIS_URL'] = [self.redis_url]
                dashboard_app.config['REDIS_URL'] = self.redis_url
            except:
                pass

            logger.info(f"✅ RQ Dashboard配置完成")
            logger.info(f"📡 Redis URL: {self.redis_url}")

            self.flask_app = dashboard_app
            self._initialized = True

            return dashboard_app

        except ImportError as e:
            logger.error(f"❌ RQ Dashboard未安装: {e}")
            logger.info("请安装RQ Dashboard: pip install rq-dashboard")
            return None
        except Exception as e:
            logger.error(f"❌ 创建RQ Dashboard失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def get_wsgi_app(self):
        """获取WSGI应用"""
        if not self._initialized:
            self.create_dashboard_app()

        if self.flask_app is None:
            logger.warning("RQ Dashboard未初始化，返回空应用")
            # 返回一个简单的WSGI应用
            def simple_app(environ, start_response):
                status = '503 Service Unavailable'
                headers = [('Content-type', 'text/html; charset=utf-8')]
                start_response(status, headers)
                return [b'<h1>RQ Dashboard Unavailable</h1><p>RQ Dashboard is not available. Please install rq-dashboard.</p>']
            return simple_app

        return self.flask_app.wsgi_app

    def is_available(self) -> bool:
        """检查RQ Dashboard是否可用"""
        return self._initialized and self.flask_app is not None

# 创建全局实例
rq_dashboard_integration = RQDashboardIntegration()

def get_rq_dashboard_integration() -> RQDashboardIntegration:
    """获取RQ Dashboard集成实例"""
    return rq_dashboard_integration

def create_rq_dashboard_wsgi():
    """创建RQ Dashboard WSGI应用"""
    integration = get_rq_dashboard_integration()
    return integration.get_wsgi_app()

def is_rq_dashboard_available() -> bool:
    """检查RQ Dashboard是否可用"""
    integration = get_rq_dashboard_integration()
    return integration.is_available()

# 测试函数
def test_rq_dashboard():
    """测试RQ Dashboard集成"""
    logger.info("🔍 测试RQ Dashboard集成...")

    integration = get_rq_dashboard_integration()
    dashboard_app = integration.create_dashboard_app()

    if dashboard_app:
        logger.info("✅ RQ Dashboard集成测试成功")
        logger.info(f"📊 Dashboard配置: {dashboard_app.config.get('RQ_DASHBOARD_REDIS_URL')}")
        return True
    else:
        logger.error("❌ RQ Dashboard集成测试失败")
        return False

if __name__ == "__main__":
    # 直接运行时进行测试
    test_rq_dashboard()
