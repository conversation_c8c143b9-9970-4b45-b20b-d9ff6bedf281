#!/usr/bin/env python
"""
独立的RQ Dashboard服务
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_standalone_rq_dashboard():
    """创建独立的RQ Dashboard服务"""
    print("🚀 创建独立的RQ Dashboard服务...\n")
    
    try:
        from fastapi import FastAPI
        from api.core.rq_dashboard_integration import create_rq_dashboard_wsgi
        from api.core.wsgi_adapter import create_simple_wsgi_adapter
        
        # 创建FastAPI应用
        app = FastAPI(title="RQ Dashboard", description="RQ任务监控面板")
        
        # 健康检查
        @app.get("/health")
        async def health_check():
            return {"status": "ok", "service": "rq-dashboard"}
        
        # 创建RQ Dashboard
        print("1. 创建RQ Dashboard...")
        wsgi_app = create_rq_dashboard_wsgi()
        asgi_app = create_simple_wsgi_adapter(wsgi_app)
        
        # 挂载到根路径
        app.mount("/", asgi_app)
        print("   ✅ RQ Dashboard已挂载到根路径")
        
        return app
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    app = create_standalone_rq_dashboard()
    
    if app:
        print("\n🎉 独立RQ Dashboard服务创建成功！")
        print("\n🚀 启动服务:")
        print("   uvicorn standalone_rq_dashboard:app --port 9181 --reload")
        print("\n🌐 访问地址:")
        print("   RQ Dashboard: http://localhost:9181")
        print("   健康检查: http://localhost:9181/health")
        
        # 如果直接运行，启动服务
        if __name__ == "__main__":
            import uvicorn
            print("\n🔄 正在启动服务...")
            uvicorn.run(app, host="0.0.0.0", port=9181, reload=False)
    else:
        print("\n❌ 独立RQ Dashboard服务创建失败")

# 导出应用供uvicorn使用
app = create_standalone_rq_dashboard()

if __name__ == "__main__":
    main()
