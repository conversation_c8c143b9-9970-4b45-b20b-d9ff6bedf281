"""
WSGI到ASGI适配器
用于在FastAPI中集成Flask应用（如RQ Dashboard）
"""

import asyncio
import threading
from typing import Callable, Dict, Any, List, Tuple
from starlette.applications import Starlette
from starlette.responses import StreamingResponse
from starlette.requests import Request
from starlette.types import <PERSON><PERSON>A<PERSON>, Receive, Scope, Send

from .debug import get_app_logger

logger = get_app_logger("wsgi_adapter")

class WSGIAdapter:
    """WSGI到ASGI适配器"""
    
    def __init__(self, wsgi_app: Callable):
        self.wsgi_app = wsgi_app
    
    async def __call__(self, scope: Scope, receive: Receive, send: Send) -> None:
        """ASGI应用入口点"""
        if scope["type"] != "http":
            # 只处理HTTP请求
            await send({
                "type": "http.response.start",
                "status": 404,
                "headers": [[b"content-type", b"text/plain"]],
            })
            await send({
                "type": "http.response.body",
                "body": b"Not Found",
            })
            return
        
        # 构建WSGI环境
        environ = self._build_environ(scope, receive)
        
        # 在线程池中运行WSGI应用
        loop = asyncio.get_event_loop()
        response_data = await loop.run_in_executor(
            None, 
            self._run_wsgi_app, 
            environ
        )
        
        # 发送响应
        await self._send_response(send, response_data)
    
    def _build_environ(self, scope: Scope, receive: Receive) -> Dict[str, Any]:
        """构建WSGI环境字典"""
        environ = {
            "REQUEST_METHOD": scope["method"],
            "SCRIPT_NAME": "",
            "PATH_INFO": scope["path"],
            "QUERY_STRING": scope.get("query_string", b"").decode("latin1"),
            "CONTENT_TYPE": "",
            "CONTENT_LENGTH": "",
            "SERVER_NAME": scope.get("server", ["localhost", None])[0],
            "SERVER_PORT": str(scope.get("server", [None, 80])[1]),
            "SERVER_PROTOCOL": f"HTTP/{scope['http_version']}",
            "wsgi.version": (1, 0),
            "wsgi.url_scheme": scope.get("scheme", "http"),
            "wsgi.input": None,  # 稍后设置
            "wsgi.errors": None,
            "wsgi.multithread": True,
            "wsgi.multiprocess": False,
            "wsgi.run_once": False,
        }
        
        # 添加请求头
        for header_name, header_value in scope.get("headers", []):
            header_name = header_name.decode("latin1")
            header_value = header_value.decode("latin1")
            
            # 转换为CGI格式
            if header_name.lower() == "content-type":
                environ["CONTENT_TYPE"] = header_value
            elif header_name.lower() == "content-length":
                environ["CONTENT_LENGTH"] = header_value
            else:
                # 其他头部转换为HTTP_*格式
                key = f"HTTP_{header_name.upper().replace('-', '_')}"
                environ[key] = header_value
        
        return environ
    
    def _run_wsgi_app(self, environ: Dict[str, Any]) -> Dict[str, Any]:
        """在同步环境中运行WSGI应用"""
        response_data = {
            "status": "500 Internal Server Error",
            "headers": [],
            "body": b"Internal Server Error"
        }
        
        def start_response(status: str, headers: List[Tuple[str, str]], exc_info=None):
            response_data["status"] = status
            response_data["headers"] = headers
        
        try:
            # 设置空的输入流
            import io
            environ["wsgi.input"] = io.BytesIO()
            environ["wsgi.errors"] = io.StringIO()
            
            # 调用WSGI应用
            app_iter = self.wsgi_app(environ, start_response)
            
            # 收集响应体
            body_parts = []
            try:
                for data in app_iter:
                    if data:
                        body_parts.append(data)
            finally:
                # 确保关闭迭代器
                if hasattr(app_iter, 'close'):
                    app_iter.close()
            
            response_data["body"] = b"".join(body_parts)
            
        except Exception as e:
            logger.error(f"WSGI应用执行失败: {e}", exc_info=True)
            response_data["status"] = "500 Internal Server Error"
            response_data["headers"] = [("Content-Type", "text/plain")]
            response_data["body"] = f"Internal Server Error: {str(e)}".encode()
        
        return response_data
    
    async def _send_response(self, send: Send, response_data: Dict[str, Any]) -> None:
        """发送HTTP响应"""
        # 解析状态码
        status_line = response_data["status"]
        status_code = int(status_line.split(" ", 1)[0])
        
        # 转换头部格式
        headers = []
        for name, value in response_data["headers"]:
            headers.append([name.encode(), value.encode()])
        
        # 发送响应开始
        await send({
            "type": "http.response.start",
            "status": status_code,
            "headers": headers,
        })
        
        # 发送响应体
        await send({
            "type": "http.response.body",
            "body": response_data["body"],
        })

def create_wsgi_adapter(wsgi_app: Callable) -> ASGIApp:
    """创建WSGI适配器"""
    return WSGIAdapter(wsgi_app)

# 简化版适配器（使用a2wsgi库，如果可用）
def create_simple_wsgi_adapter(wsgi_app: Callable) -> ASGIApp:
    """创建简化的WSGI适配器"""
    try:
        # 尝试使用a2wsgi库
        from a2wsgi import WSGIMiddleware
        return WSGIMiddleware(wsgi_app)
    except ImportError:
        # 如果没有a2wsgi，使用自定义适配器
        logger.info("a2wsgi未安装，使用自定义WSGI适配器")
        return create_wsgi_adapter(wsgi_app)

if __name__ == "__main__":
    # 测试适配器
    def simple_wsgi_app(environ, start_response):
        status = '200 OK'
        headers = [('Content-type', 'text/plain')]
        start_response(status, headers)
        return [b'Hello from WSGI!']
    
    adapter = create_wsgi_adapter(simple_wsgi_app)
    print("WSGI适配器创建成功")
