#!/usr/bin/env python
"""
测试调度器冲突修复
"""

import sys
import os
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_scheduler_cleanup():
    """测试调度器清理功能"""
    print("🧹 测试调度器清理功能...")
    
    try:
        from api.core.scheduler import TaskScheduler
        
        # 创建调度器实例
        scheduler = TaskScheduler()
        
        # 测试清理功能
        print("执行调度器清理...")
        scheduler._cleanup_existing_scheduler()
        print("✅ 调度器清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 调度器清理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scheduler_start_stop():
    """测试调度器启动和停止"""
    print("\n🚀 测试调度器启动和停止...")
    
    try:
        from api.core.scheduler import start_scheduler, stop_scheduler
        
        # 测试启动
        print("启动调度器...")
        if start_scheduler():
            print("✅ 调度器启动成功")
            
            # 等待一下
            time.sleep(3)
            
            # 测试停止
            print("停止调度器...")
            if stop_scheduler():
                print("✅ 调度器停止成功")
                return True
            else:
                print("❌ 调度器停止失败")
                return False
        else:
            print("❌ 调度器启动失败")
            return False
        
    except Exception as e:
        print(f"❌ 调度器启动停止测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_redis_scheduler_keys():
    """测试Redis中的调度器键"""
    print("\n🔍 检查Redis中的调度器键...")
    
    try:
        from api.core.rq_app import get_redis_connection
        
        redis_conn = get_redis_connection()
        
        # 查找调度器相关的键
        scheduler_keys = redis_conn.keys("rq:scheduler:*")
        print(f"找到 {len(scheduler_keys)} 个调度器相关键:")
        
        for key in scheduler_keys:
            if isinstance(key, bytes):
                key_str = key.decode('utf-8', errors='ignore')
            else:
                key_str = str(key)
            print(f"  - {key_str}")
        
        # 检查调度器注册表
        registry_key = "rq:scheduler:schedulers"
        if redis_conn.exists(registry_key):
            schedulers = redis_conn.smembers(registry_key)
            print(f"注册表中的调度器: {schedulers}")
        else:
            print("调度器注册表不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis键检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 RQ调度器冲突修复测试开始...\n")
    
    # 测试Redis键状态
    redis_ok = test_redis_scheduler_keys()
    
    # 测试清理功能
    cleanup_ok = test_scheduler_cleanup()
    
    # 测试启动停止
    start_stop_ok = test_scheduler_start_stop()
    
    # 再次检查Redis键状态
    print("\n" + "="*50)
    print("📋 测试后Redis状态:")
    test_redis_scheduler_keys()
    
    # 总结
    print("\n" + "="*50)
    print("📋 测试结果总结:")
    print(f"  Redis键检查: {'✅ 正常' if redis_ok else '❌ 失败'}")
    print(f"  调度器清理: {'✅ 正常' if cleanup_ok else '❌ 失败'}")
    print(f"  启动停止测试: {'✅ 正常' if start_stop_ok else '❌ 失败'}")
    
    if redis_ok and cleanup_ok and start_stop_ok:
        print("\n🎉 所有测试通过！调度器冲突问题已修复。")
    else:
        print("\n⚠️  部分测试失败，请检查错误信息。")

if __name__ == "__main__":
    main()
