#!/usr/bin/env python
"""
最终RQ Dashboard测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_final():
    """最终测试"""
    print("🔧 最终RQ Dashboard测试...\n")
    
    try:
        # 设置环境变量
        os.environ['RQ_DASHBOARD_REDIS_URL'] = 'redis://:yhb25IEz@10.100.4.17:6379/3'
        
        # 测试集成模块
        from api.core.rq_dashboard_integration import RQDashboardIntegration
        
        integration = RQDashboardIntegration()
        app = integration.create_dashboard_app()
        
        if app:
            print("✅ RQ Dashboard应用创建成功")
            print(f"   应用类型: {type(app)}")
            print(f"   配置: {app.config.get('RQ_DASHBOARD_REDIS_URL')}")
            
            # 测试路由
            print("\n🔍 测试路由...")
            for rule in app.url_map.iter_rules():
                print(f"   - {rule.rule} -> {rule.endpoint}")
            
            # 测试响应
            print("\n🔍 测试响应...")
            with app.test_client() as client:
                response = client.get('/')
                print(f"   GET / -> 状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ RQ Dashboard响应正常")
                    return True
                else:
                    print(f"❌ 响应异常: {response.status_code}")
                    if response.data:
                        print(f"   错误内容: {response.data[:200]}")
                    return False
        else:
            print("❌ RQ Dashboard应用创建失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_final()
    if success:
        print("\n🎉 测试成功！RQ Dashboard应该可以正常工作了。")
    else:
        print("\n❌ 测试失败，请检查错误信息。")
