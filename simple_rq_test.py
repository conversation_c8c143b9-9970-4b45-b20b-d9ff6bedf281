#!/usr/bin/env python
"""
简单的RQ Dashboard测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_rq_dashboard_simple():
    """简单测试RQ Dashboard"""
    print("🔍 简单测试RQ Dashboard...")
    
    try:
        # 1. 测试导入
        print("1. 测试rq_dashboard导入...")
        import rq_dashboard
        print("✅ rq_dashboard导入成功")
        
        # 2. 检查属性
        print("2. 检查rq_dashboard属性...")
        attrs = [attr for attr in dir(rq_dashboard) if not attr.startswith('_')]
        print(f"   属性: {attrs}")
        
        # 3. 测试蓝图导入
        print("3. 测试蓝图导入...")
        try:
            from rq_dashboard import blueprint
            print("✅ blueprint导入成功")
        except ImportError:
            try:
                from rq_dashboard.web import blueprint
                print("✅ web.blueprint导入成功")
            except ImportError:
                print("❌ 无法导入blueprint")
                return False
        
        # 4. 创建Flask应用
        print("4. 创建Flask应用...")
        from flask import Flask
        app = Flask(__name__)
        app.register_blueprint(blueprint)
        
        # 5. 配置Redis
        app.config['RQ_DASHBOARD_REDIS_URL'] = 'redis://:yhb25IEz@10.100.4.17:6379/3'
        
        print("✅ Flask应用创建成功")
        
        # 6. 测试路由
        print("5. 测试路由...")
        with app.test_client() as client:
            response = client.get('/')
            print(f"   GET / -> 状态码: {response.status_code}")
            if response.status_code == 200:
                print(f"   响应长度: {len(response.data)} bytes")
                print("✅ RQ Dashboard响应正常")
                return True
            else:
                print(f"❌ 响应异常: {response.status_code}")
                return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """测试集成模块"""
    print("\n🔍 测试集成模块...")
    
    try:
        from api.core.rq_dashboard_integration import RQDashboardIntegration
        
        integration = RQDashboardIntegration()
        app = integration.create_dashboard_app()
        
        if app:
            print("✅ 集成模块创建应用成功")
            
            # 测试应用
            with app.test_client() as client:
                response = client.get('/')
                print(f"   GET / -> 状态码: {response.status_code}")
                return response.status_code == 200
        else:
            print("❌ 集成模块创建应用失败")
            return False
            
    except Exception as e:
        print(f"❌ 集成模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 简单RQ Dashboard测试开始...\n")
    
    # 简单测试
    simple_ok = test_rq_dashboard_simple()
    
    # 集成测试
    integration_ok = test_integration()
    
    # 总结
    print("\n" + "="*40)
    print("📋 测试结果:")
    print(f"  简单测试: {'✅ 通过' if simple_ok else '❌ 失败'}")
    print(f"  集成测试: {'✅ 通过' if integration_ok else '❌ 失败'}")
    
    if simple_ok and integration_ok:
        print("\n🎉 所有测试通过！")
    else:
        print("\n⚠️  测试失败，请检查错误信息。")

if __name__ == "__main__":
    main()
