#!/usr/bin/env python
"""
测试Redis连接和编码问题修复
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_redis_connections():
    """测试Redis连接"""
    print("🔧 测试Redis连接和编码修复...")
    
    try:
        from api.core.rq_app import get_redis_connection, get_redis_connection_for_monitor
        
        # 测试RQ Redis连接
        print("\n1. 测试RQ Redis连接...")
        rq_redis = get_redis_connection()
        rq_redis.ping()
        print("✅ RQ Redis连接正常")
        
        # 测试监控Redis连接
        print("\n2. 测试监控Redis连接...")
        monitor_redis = get_redis_connection_for_monitor()
        monitor_redis.ping()
        print("✅ 监控Redis连接正常")
        
        # 测试基本操作
        print("\n3. 测试基本Redis操作...")
        test_key = "test:encoding:fix"
        test_value = "测试中文编码"
        
        monitor_redis.set(test_key, test_value)
        retrieved_value = monitor_redis.get(test_key)
        print(f"✅ 设置值: {test_value}")
        print(f"✅ 获取值: {retrieved_value}")
        
        # 清理测试数据
        monitor_redis.delete(test_key)
        print("✅ 清理测试数据完成")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis连接测试失败: {e}")
        return False

def test_task_manager():
    """测试任务管理器"""
    print("\n🎯 测试任务管理器...")
    
    try:
        from api.core.task_manager import task_manager, submit_task, get_task_status
        
        # 定义一个简单的测试任务
        def simple_test_task(message):
            import time
            time.sleep(1)
            return f"任务完成: {message}"
        
        # 提交任务
        print("\n1. 提交测试任务...")
        task_id = submit_task(
            simple_test_task,
            "编码测试",
            queue="default",
            timeout=30,
            description="编码修复测试任务"
        )
        print(f"✅ 任务已提交，ID: {task_id}")
        
        # 获取任务状态
        print("\n2. 获取任务状态...")
        status = get_task_status(task_id)
        print(f"✅ 任务状态: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_task_monitor():
    """测试任务监控器"""
    print("\n📊 测试任务监控器...")
    
    try:
        from api.core.task_monitor import get_task_monitor
        
        task_monitor = get_task_monitor()
        
        # 测试获取活跃任务
        print("\n1. 获取活跃任务...")
        active_tasks = task_monitor.get_active_tasks()
        print(f"✅ 活跃任务数量: {len(active_tasks)}")
        
        # 测试获取已完成任务
        print("\n2. 获取已完成任务...")
        completed_tasks = task_monitor.get_completed_tasks(limit=5)
        print(f"✅ 已完成任务数量: {len(completed_tasks)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务监控器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 Redis编码问题修复测试开始...\n")
    
    # 测试Redis连接
    redis_ok = test_redis_connections()
    
    if redis_ok:
        # 测试任务管理器
        task_manager_ok = test_task_manager()
        
        # 测试任务监控器
        task_monitor_ok = test_task_monitor()
        
        # 总结
        print("\n" + "="*50)
        print("📋 测试结果总结:")
        print(f"  Redis连接: {'✅ 正常' if redis_ok else '❌ 失败'}")
        print(f"  任务管理器: {'✅ 正常' if task_manager_ok else '❌ 失败'}")
        print(f"  任务监控器: {'✅ 正常' if task_monitor_ok else '❌ 失败'}")
        
        if redis_ok and task_manager_ok and task_monitor_ok:
            print("\n🎉 所有测试通过！编码问题已修复。")
        else:
            print("\n⚠️  部分测试失败，请检查错误信息。")
    else:
        print("\n❌ Redis连接失败，请检查Redis服务和配置。")

if __name__ == "__main__":
    main()
