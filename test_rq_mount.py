#!/usr/bin/env python
"""
测试RQ Dashboard挂载
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_app_routes():
    """测试应用路由"""
    print("🔍 测试FastAPI应用路由...")
    
    try:
        from api.main import app
        
        print("✅ FastAPI应用导入成功")
        
        # 检查所有路由
        print("\n📋 应用路由列表:")
        for route in app.routes:
            if hasattr(route, 'path'):
                print(f"  - {route.path} ({type(route).__name__})")
            elif hasattr(route, 'path_regex'):
                print(f"  - {route.path_regex.pattern} ({type(route).__name__})")
        
        # 检查是否有/rq挂载
        rq_mounted = False
        for route in app.routes:
            if hasattr(route, 'path') and route.path == "/rq":
                rq_mounted = True
                print(f"\n✅ 找到RQ Dashboard挂载: {route.path}")
                print(f"   类型: {type(route).__name__}")
                if hasattr(route, 'app'):
                    print(f"   子应用: {type(route.app).__name__}")
                break
        
        if not rq_mounted:
            print("\n❌ 未找到RQ Dashboard挂载")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rq_dashboard_direct():
    """直接测试RQ Dashboard"""
    print("\n🔍 直接测试RQ Dashboard...")
    
    try:
        from api.core.rq_dashboard_integration import test_rq_dashboard
        
        success = test_rq_dashboard()
        if success:
            print("✅ RQ Dashboard直接测试成功")
        else:
            print("❌ RQ Dashboard直接测试失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_wsgi_adapter():
    """测试WSGI适配器"""
    print("\n🔍 测试WSGI适配器...")
    
    try:
        from api.core.wsgi_adapter import create_simple_wsgi_adapter
        
        # 创建一个简单的WSGI应用
        def simple_wsgi(environ, start_response):
            status = '200 OK'
            headers = [('Content-type', 'text/plain')]
            start_response(status, headers)
            return [b'Hello from WSGI!']
        
        # 测试适配器
        asgi_app = create_simple_wsgi_adapter(simple_wsgi)
        print("✅ WSGI适配器创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ WSGI适配器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 RQ Dashboard挂载测试开始...\n")
    
    # 测试WSGI适配器
    adapter_ok = test_wsgi_adapter()
    
    # 测试RQ Dashboard
    dashboard_ok = test_rq_dashboard_direct()
    
    # 测试应用路由
    routes_ok = test_app_routes()
    
    # 总结
    print("\n" + "="*50)
    print("📋 测试结果总结:")
    print(f"  WSGI适配器: {'✅ 正常' if adapter_ok else '❌ 失败'}")
    print(f"  RQ Dashboard: {'✅ 正常' if dashboard_ok else '❌ 失败'}")
    print(f"  应用路由: {'✅ 正常' if routes_ok else '❌ 失败'}")
    
    if adapter_ok and dashboard_ok and routes_ok:
        print("\n🎉 所有测试通过！")
        print("🌐 RQ Dashboard应该可以通过 http://localhost:8000/rq 访问")
    else:
        print("\n⚠️  部分测试失败，请检查错误信息。")
        
        if not dashboard_ok:
            print("💡 提示: 请确保已安装 rq-dashboard: pip install rq-dashboard")
        
        if not routes_ok:
            print("💡 提示: 检查应用挂载顺序，确保RQ Dashboard在静态文件之前挂载")

if __name__ == "__main__":
    main()
