# 统一任务管理系统 - Flask-RQ

## 🎯 概述

本项目已成功将所有任务管理统一到Flask-RQ系统，替代了原有的多套任务管理方案（Celery、BackgroundTasks、Threading等），实现了真正的统一任务管理。

## 📋 迁移完成的内容

### 1. 核心任务管理器 (`api/core/task_manager.py`)
- ✅ 完善的Flask-RQ任务管理器
- ✅ 支持任务提交、状态查询、取消、重试
- ✅ 队列管理和任务统计
- ✅ 统一的任务装饰器

### 2. 定时任务调度器 (`api/core/scheduler.py`)
- ✅ 基于RQ Scheduler的定时任务系统
- ✅ 支持单次任务和重复任务调度
- ✅ 替代Celery Beat功能
- ✅ 自动设置默认定时任务

### 3. 统一任务API (`api/routers/unified_tasks.py`)
- ✅ 完整的任务管理API接口
- ✅ 即时任务、定时任务、重复任务管理
- ✅ 队列管理和系统信息查询
- ✅ 调度器控制接口

### 4. 任务模块迁移
- ✅ `api/tasks/sync_tasks.py` - 同步任务使用Flask-RQ装饰器
- ✅ `api/tasks/excel_material_processor.py` - 已使用Flask-RQ
- ✅ `api/routers/tasks.py` - 使用统一任务管理器
- ✅ `api/routers/sync.py` - 替代BackgroundTasks

### 5. 主应用集成 (`api/main.py`)
- ✅ 注册统一任务管理路由
- ✅ 启动时自动启动调度器
- ✅ 设置默认定时任务
- ✅ 应用关闭时停止调度器

## 🚀 使用方法

### 1. 即时任务提交

```python
from api.core.task_manager import submit_task
from api.tasks.sync_tasks import sync_materials

# 提交任务
task_id = submit_task(
    sync_materials,
    queue="default",
    timeout=1800,
    description="材料数据同步任务"
)
```

### 2. 定时任务调度

```python
from api.core.scheduler import schedule_task, schedule_recurring_task
from datetime import datetime, timedelta

# 调度单次任务
task_id = schedule_task(
    sync_materials,
    datetime.now() + timedelta(hours=1),
    queue="default",
    description="1小时后执行同步"
)

# 调度重复任务
task_id = schedule_recurring_task(
    sync_materials,
    86400,  # 24小时间隔
    queue="default",
    description="每日同步任务"
)
```

### 3. 任务装饰器

```python
from api.core.task_manager import job

@job(queue='default', timeout=300, result_ttl=3600, description="示例任务")
def my_task(param1, param2):
    # 任务逻辑
    return "任务完成"
```

## 📡 API接口

### 统一任务管理API (`/api/unified-tasks`)

#### 即时任务
- `POST /api/unified-tasks/submit` - 提交任务
- `GET /api/unified-tasks/status/{task_id}` - 获取任务状态
- `POST /api/unified-tasks/cancel/{task_id}` - 取消任务
- `POST /api/unified-tasks/retry/{task_id}` - 重试任务

#### 定时任务
- `POST /api/unified-tasks/schedule` - 调度单次任务
- `POST /api/unified-tasks/schedule-recurring` - 调度重复任务
- `DELETE /api/unified-tasks/scheduled/{task_id}` - 取消定时任务
- `GET /api/unified-tasks/scheduled` - 获取定时任务列表

#### 队列管理
- `GET /api/unified-tasks/queues` - 获取队列信息
- `GET /api/unified-tasks/tasks/{status}` - 根据状态获取任务
- `DELETE /api/unified-tasks/queues/{queue_name}/clear` - 清空队列

#### 调度器管理
- `POST /api/unified-tasks/scheduler/start` - 启动调度器
- `POST /api/unified-tasks/scheduler/stop` - 停止调度器
- `POST /api/unified-tasks/scheduler/setup-defaults` - 设置默认任务

#### 系统信息
- `GET /api/unified-tasks/info` - 获取系统信息

### 请求示例

```bash
# 提交同步任务
curl -X POST "http://localhost:8000/api/unified-tasks/submit" \
  -H "Content-Type: application/json" \
  -d '{
    "task_type": "sync_materials",
    "queue": "default",
    "timeout": 1800,
    "description": "手动触发的同步任务"
  }'

# 调度定时任务
curl -X POST "http://localhost:8000/api/unified-tasks/schedule" \
  -H "Content-Type: application/json" \
  -d '{
    "task_type": "sync_materials",
    "scheduled_time": "2024-01-01T10:00:00",
    "queue": "default",
    "description": "定时同步任务"
  }'

# 获取任务状态
curl "http://localhost:8000/api/unified-tasks/status/{task_id}"

# 获取队列信息
curl "http://localhost:8000/api/unified-tasks/queues"
```

## 🔧 配置说明

### 队列配置
- `default` - 默认队列，处理一般任务
- `high` - 高优先级队列，处理重要任务
- `low` - 低优先级队列，处理后台任务

### 默认定时任务
系统启动时会自动设置以下定时任务：
- 每日材料数据同步（24小时间隔）

### Redis配置
任务系统使用Redis作为消息代理和结果存储：
```python
REDIS_HOST = "***********"
REDIS_PORT = "6379"
REDIS_PASSWORD = "yhb25IEz"
REDIS_DB = "3"
```

## 🎯 优势

### 1. 统一管理
- 所有任务都通过Flask-RQ管理
- 统一的API接口和监控
- 一致的任务状态和错误处理

### 2. 功能完整
- 即时任务执行
- 定时任务调度
- 重复任务管理
- 队列优先级控制

### 3. 易于使用
- 简单的装饰器语法
- 丰富的API接口
- 完善的错误处理

### 4. 高性能
- 基于Redis的高性能队列
- 支持多Worker并发处理
- 灵活的超时和重试机制

## 🔄 迁移对比

| 功能 | 原系统 | 新系统 |
|------|--------|--------|
| 即时任务 | Celery + BackgroundTasks | Flask-RQ |
| 定时任务 | Celery Beat | RQ Scheduler |
| 任务监控 | 多套系统 | 统一监控 |
| API接口 | 分散的接口 | 统一API |
| 配置管理 | 复杂配置 | 简化配置 |

## 📝 注意事项

### 1. 兼容性
- 保留了原有的任务路由接口
- 现有的前端代码无需修改
- 平滑迁移，无中断服务

### 2. 监控
- RQ Dashboard可以独立启动监控任务
- 任务监控器自动同步RQ任务状态
- 完整的任务生命周期跟踪

### 3. 扩展性
- 易于添加新的任务类型
- 支持自定义队列和优先级
- 灵活的调度策略

## 🚀 启动说明

### 1. 启动后端服务
```bash
uvicorn api.main:app --reload
```

### 2. 启动RQ Worker
```bash
python start_rq_worker.py
```

### 3. 启动RQ Dashboard（可选）
```bash
rq-dashboard --redis-url redis://:yhb25IEz@***********:6379/3
```

## 📊 监控和管理

### 1. 任务状态监控
- 通过API接口实时查询任务状态
- 支持按状态筛选任务列表
- 完整的任务执行历史

### 2. 队列管理
- 实时查看队列长度和状态
- 支持清空特定状态的任务
- 队列性能统计

### 3. 调度器管理
- 启动/停止调度器
- 查看已调度的任务
- 取消定时任务

## 🎉 总结

通过统一任务管理系统，我们实现了：

1. **简化架构** - 从多套任务系统统一到Flask-RQ
2. **提升性能** - 基于Redis的高性能任务队列
3. **增强功能** - 完整的定时任务和重复任务支持
4. **改善体验** - 统一的API接口和监控界面
5. **便于维护** - 单一的任务管理系统，降低维护成本

所有任务现在都通过Flask-RQ统一管理，提供了更好的性能、更丰富的功能和更简单的使用体验。
