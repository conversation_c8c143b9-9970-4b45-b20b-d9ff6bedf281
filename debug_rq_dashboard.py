#!/usr/bin/env python
"""
调试RQ Dashboard 404问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_rq_dashboard():
    """调试RQ Dashboard"""
    print("🔍 调试RQ Dashboard 404问题...\n")
    
    # 1. 检查rq_dashboard导入
    print("1. 检查rq_dashboard导入...")
    try:
        import rq_dashboard
        print(f"✅ rq_dashboard导入成功")
        print(f"   版本: {getattr(rq_dashboard, '__version__', '未知')}")
        print(f"   路径: {rq_dashboard.__file__}")
        print(f"   属性: {[attr for attr in dir(rq_dashboard) if not attr.startswith('_')]}")
    except Exception as e:
        print(f"❌ rq_dashboard导入失败: {e}")
        return False
    
    # 2. 检查Flask应用创建
    print("\n2. 检查Flask应用创建...")
    try:
        from api.core.rq_dashboard_integration import RQDashboardIntegration
        
        integration = RQDashboardIntegration()
        flask_app = integration.create_dashboard_app()
        
        if flask_app:
            print("✅ Flask应用创建成功")
            print(f"   应用类型: {type(flask_app)}")
            print(f"   配置: {dict(flask_app.config)}")
            
            # 检查路由
            print("   路由列表:")
            for rule in flask_app.url_map.iter_rules():
                print(f"     - {rule.rule} -> {rule.endpoint}")
        else:
            print("❌ Flask应用创建失败")
            return False
            
    except Exception as e:
        print(f"❌ Flask应用创建出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 3. 测试Flask应用响应
    print("\n3. 测试Flask应用响应...")
    try:
        with flask_app.test_client() as client:
            # 测试根路径
            response = client.get('/')
            print(f"   GET / -> 状态码: {response.status_code}")
            if response.status_code == 200:
                print(f"   响应长度: {len(response.data)} bytes")
            else:
                print(f"   响应内容: {response.data[:200]}")
            
            # 测试其他可能的路径
            for path in ['/', '/rq', '/dashboard']:
                try:
                    resp = client.get(path)
                    print(f"   GET {path} -> {resp.status_code}")
                except Exception as e:
                    print(f"   GET {path} -> 错误: {e}")
                    
    except Exception as e:
        print(f"❌ Flask应用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 4. 检查WSGI适配器
    print("\n4. 检查WSGI适配器...")
    try:
        from api.core.wsgi_adapter import create_simple_wsgi_adapter
        
        wsgi_app = flask_app.wsgi_app
        asgi_app = create_simple_wsgi_adapter(wsgi_app)
        
        print(f"✅ WSGI适配器创建成功")
        print(f"   WSGI应用: {type(wsgi_app)}")
        print(f"   ASGI适配器: {type(asgi_app)}")
        
    except Exception as e:
        print(f"❌ WSGI适配器创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 5. 检查FastAPI挂载
    print("\n5. 检查FastAPI挂载...")
    try:
        from api.main import app
        
        print("✅ FastAPI应用导入成功")
        
        # 检查挂载的路由
        mount_found = False
        for route in app.routes:
            if hasattr(route, 'path'):
                if route.path == "/rq":
                    mount_found = True
                    print(f"✅ 找到RQ挂载: {route.path}")
                    print(f"   路由类型: {type(route)}")
                    if hasattr(route, 'app'):
                        print(f"   子应用类型: {type(route.app)}")
                    break
        
        if not mount_found:
            print("❌ 未找到RQ挂载")
            print("   所有路由:")
            for route in app.routes:
                if hasattr(route, 'path'):
                    print(f"     - {route.path} ({type(route).__name__})")
            return False
            
    except Exception as e:
        print(f"❌ FastAPI应用检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_direct_wsgi():
    """直接测试WSGI应用"""
    print("\n🧪 直接测试WSGI应用...")
    
    try:
        from api.core.rq_dashboard_integration import create_rq_dashboard_wsgi
        
        wsgi_app = create_rq_dashboard_wsgi()
        
        # 创建测试环境
        environ = {
            'REQUEST_METHOD': 'GET',
            'PATH_INFO': '/',
            'QUERY_STRING': '',
            'CONTENT_TYPE': '',
            'CONTENT_LENGTH': '',
            'SERVER_NAME': 'localhost',
            'SERVER_PORT': '8000',
            'wsgi.version': (1, 0),
            'wsgi.url_scheme': 'http',
            'wsgi.input': None,
            'wsgi.errors': None,
        }
        
        # 测试WSGI应用
        response_data = {'status': None, 'headers': []}
        
        def start_response(status, headers):
            response_data['status'] = status
            response_data['headers'] = headers
        
        import io
        environ['wsgi.input'] = io.BytesIO()
        environ['wsgi.errors'] = io.StringIO()
        
        result = wsgi_app(environ, start_response)
        
        print(f"✅ WSGI应用响应成功")
        print(f"   状态: {response_data['status']}")
        print(f"   头部数量: {len(response_data['headers'])}")
        
        # 收集响应体
        body_parts = []
        for data in result:
            if data:
                body_parts.append(data)
        
        body = b''.join(body_parts)
        print(f"   响应体长度: {len(body)} bytes")
        
        if len(body) > 0:
            print(f"   响应体预览: {body[:200]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接WSGI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 RQ Dashboard 404问题调试开始...\n")
    
    # 调试RQ Dashboard
    dashboard_ok = debug_rq_dashboard()
    
    # 直接测试WSGI
    wsgi_ok = test_direct_wsgi()
    
    # 总结
    print("\n" + "="*50)
    print("📋 调试结果总结:")
    print(f"  RQ Dashboard: {'✅ 正常' if dashboard_ok else '❌ 失败'}")
    print(f"  WSGI测试: {'✅ 正常' if wsgi_ok else '❌ 失败'}")
    
    if dashboard_ok and wsgi_ok:
        print("\n🎉 所有组件正常！")
        print("💡 可能的问题:")
        print("   1. 应用需要重启")
        print("   2. 浏览器缓存问题")
        print("   3. 路径问题（确保访问 /rq 而不是 /rq/）")
    else:
        print("\n⚠️  发现问题，请检查上述错误信息。")

if __name__ == "__main__":
    main()
