#!/usr/bin/env python
"""
最小化测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_minimal():
    """最小化测试"""
    print("🔍 最小化测试...\n")
    
    try:
        # 直接测试RQ Dashboard
        print("1. 测试RQ Dashboard创建...")
        from api.core.rq_dashboard_integration import create_rq_dashboard_wsgi
        
        wsgi_app = create_rq_dashboard_wsgi()
        if wsgi_app:
            print("   ✅ RQ Dashboard WSGI应用创建成功")
            
            # 测试WSGI应用
            environ = {
                'REQUEST_METHOD': 'GET',
                'PATH_INFO': '/',
                'QUERY_STRING': '',
                'CONTENT_TYPE': '',
                'CONTENT_LENGTH': '',
                'SERVER_NAME': 'localhost',
                'SERVER_PORT': '8000',
                'wsgi.version': (1, 0),
                'wsgi.url_scheme': 'http',
            }
            
            import io
            environ['wsgi.input'] = io.BytesIO()
            environ['wsgi.errors'] = io.StringIO()
            
            response_data = {'status': None, 'headers': []}
            
            def start_response(status, headers):
                response_data['status'] = status
                response_data['headers'] = headers
            
            try:
                result = wsgi_app(environ, start_response)
                body_parts = []
                for data in result:
                    if data:
                        body_parts.append(data)
                
                body = b''.join(body_parts)
                print(f"   状态: {response_data['status']}")
                print(f"   响应体长度: {len(body)} bytes")
                
                if response_data['status'].startswith('200'):
                    print("   ✅ RQ Dashboard WSGI响应正常")
                    return True
                else:
                    print(f"   ❌ RQ Dashboard WSGI响应异常: {response_data['status']}")
                    return False
                    
            except Exception as e:
                print(f"   ❌ WSGI测试失败: {e}")
                return False
        else:
            print("   ❌ RQ Dashboard WSGI应用创建失败")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_minimal()
    if success:
        print("\n🎉 最小化测试成功！")
        print("💡 RQ Dashboard本身工作正常，问题可能在FastAPI集成")
    else:
        print("\n❌ 最小化测试失败")
