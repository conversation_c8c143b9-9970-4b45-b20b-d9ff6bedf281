#!/usr/bin/env python
"""
简化的FastAPI测试
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simple_fastapi():
    """测试简化的FastAPI应用"""
    print("🔍 测试简化的FastAPI应用...\n")
    
    try:
        from fastapi import FastAPI
        from fastapi.testclient import TestClient
        
        # 创建简化的FastAPI应用
        app = FastAPI()
        
        # 添加健康检查
        @app.get("/health")
        async def health_check():
            return {"status": "ok"}
        
        # 集成RQ Dashboard
        print("1. 集成RQ Dashboard...")
        from api.core.rq_dashboard_integration import create_rq_dashboard_wsgi
        from api.core.wsgi_adapter import create_simple_wsgi_adapter
        
        wsgi_app = create_rq_dashboard_wsgi()
        asgi_app = create_simple_wsgi_adapter(wsgi_app)
        
        # 挂载RQ Dashboard
        app.mount("/rq", asgi_app)
        print("   ✅ RQ Dashboard已挂载")
        
        # 测试应用
        print("\n2. 测试应用...")
        client = TestClient(app)
        
        # 测试健康检查
        response = client.get("/health")
        print(f"   GET /health -> {response.status_code}")
        if response.status_code == 200:
            print(f"   响应: {response.json()}")
        
        # 测试RQ Dashboard
        response = client.get("/rq")
        print(f"   GET /rq -> {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ RQ Dashboard访问成功")
            print(f"   响应长度: {len(response.content)} bytes")
            return True
        else:
            print(f"   ❌ RQ Dashboard访问失败: {response.status_code}")
            if response.content:
                print(f"   响应内容: {response.content[:200]}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_fastapi()
    if success:
        print("\n🎉 简化FastAPI测试成功！")
        print("💡 问题可能在于主应用的复杂性或启动过程")
    else:
        print("\n❌ 简化FastAPI测试失败")
