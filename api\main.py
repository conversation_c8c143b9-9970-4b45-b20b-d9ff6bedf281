
import os
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
import time

from sqlalchemy import false, true
from .core.debug import get_app_logger
from .routers import upload, sync, chat, items, tasks, excel, flask_rq2_demo, excel_material, unified_tasks  # 导入路由模块
from .core import globals # 导入全局变量模块
from .config import USE_LOCAL_EMBEDDING # 导入配置
from .core.globals import BGEM3EmbeddingFunction # 导入模型类
from .core.rq_app import get_rq_instance, get_queue, get_scheduler # 导入RQ应用

# 获取调试设置和日志记录器
logger = get_app_logger("api")

# 创建FastAPI应用
app = FastAPI(
    title="材料价格库系统API",
    description="用于管理材料价格信息的API",
    version="1.0.0",
    debug=True
)

# 添加CORS中间件
# Add to existing CORS config
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Flower将独立启动，不再使用代理中间件

# 添加请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    if True:
        start_time = time.time()

        # 记录请求信息
        logger.info(f"开始处理请求: {request.method} {request.url.path}")
        if True:
            logger.debug(f"请求头: {request.headers}")

        # 处理请求
        response = await call_next(request)

        # 计算处理时间
        process_time = (time.time() - start_time) * 1000
        formatted_process_time = f"{process_time:.2f}"

        # 记录响应信息
        logger.info(f"请求处理完成: {request.method} {request.url.path} - 状态: {response.status_code} - 耗时: {formatted_process_time}ms")

        return response
    else:
        return await call_next(request)

# 注册路由
app.include_router(sync.router,prefix="/api")
app.include_router(upload.router,prefix="/api")
app.include_router(chat.router,prefix="/api")
app.include_router(tasks.router,prefix="/api")
app.include_router(excel.router,prefix="/api")
app.include_router(flask_rq2_demo.router,prefix="/api")
app.include_router(excel_material.router,prefix="/api")
app.include_router(unified_tasks.router,prefix="/api")
app.include_router(items.router)

# 配置静态文件服务
from fastapi.staticfiles import StaticFiles
app.mount("/", StaticFiles(directory="www"), name="www")  # 将www目录下的文件暴露为静态资源


# 应用启动事件
@app.on_event("startup")
async def startup_event():
    import time
    logger.info("应用启动中...")

    # 初始化数据库表
    try:
        from .core.database import create_db_and_tables
        create_db_and_tables()
        logger.info("数据库表初始化完成")
    except Exception as e:
        logger.error(f"初始化数据库表时出错: {e}")

    # 输出AI配置信息
    try:
        from .config import (
            CHAT_API_URL, CHAT_API_KEY, CHAT_MODEL, CHAT_TEMPERATURE, CHAT_MAX_TOKENS,
            EMBEDDING_API_URL, EMBEDDING_API_KEY, EMBEDDING_MODEL, EMBEDDING_BATCH_SIZE,
            ENABLE_SEMANTIC_SEARCH, ENABLE_CHAT_ASSISTANT
        )
        logger.info(f"AI配置加载完成:")
        logger.info(f"聊天模型配置:")
        logger.info(f"  - API地址: {CHAT_API_URL}")
        logger.info(f"  - API密钥: {'已配置' if CHAT_API_KEY else '未配置'}")
        logger.info(f"  - 模型: {CHAT_MODEL}, 温度: {CHAT_TEMPERATURE}, 最大Token: {CHAT_MAX_TOKENS}")
        logger.info(f"嵌入模型配置:")
        logger.info(f"  - API地址: {EMBEDDING_API_URL}")
        logger.info(f"  - API密钥: {'已配置' if EMBEDDING_API_KEY else '未配置'}")
        logger.info(f"  - 模型: {EMBEDDING_MODEL}, 批处理大小: {EMBEDDING_BATCH_SIZE}")
        logger.info(f"功能配置:")
        logger.info(f"  - 语义搜索: {'启用' if ENABLE_SEMANTIC_SEARCH else '禁用'}")
        logger.info(f"  - 聊天助手: {'启用' if ENABLE_CHAT_ASSISTANT else '禁用'}")
    except Exception as e:
        logger.error(f"加载AI配置时出错: {e}")

    # 加载本地嵌入模型（如果配置了）
    if USE_LOCAL_EMBEDDING:
        logger.info("检测到 USE_LOCAL_EMBEDDING=True，尝试加载本地 BGE-M3 模型...")
        try:
            # 确保 BGEM3EmbeddingFunction 已成功导入
            if BGEM3EmbeddingFunction is None:
                 raise ImportError("BGEM3EmbeddingFunction 未能从 milvus_model.hybrid 或 FlagEmbedding 导入")

            # 获取模型路径 (相对于 api 目录)
            base_path = os.path.dirname(__file__) # api 目录
            model_path = os.path.join(base_path, "data", "model", "BGE-M3")
            os.makedirs(model_path, exist_ok=True)
            logger.info(f"本地模型路径: {model_path}")

            # 如果模型文件不存在则下载 (可选，如果需要自动下载)
            # if not os.path.exists(os.path.join(model_path, "config.json")):
            #     logger.info("本地模型文件不存在，尝试从 Hugging Face 下载...")
            #     from transformers import AutoModel
            #     # 注意：cache_dir 应指向 BGE-M3 目录的上级，让 transformers 创建 BGE-M3 目录
            #     cache_dir_for_download = os.path.dirname(model_path)
            #     model = AutoModel.from_pretrained("BAAI/bge-m3", cache_dir=cache_dir_for_download)
            #     logger.info("模型下载完成。")
            # else:
            #     logger.info("本地模型文件已存在。")

            # 强制离线模式
            os.environ["TRANSFORMERS_OFFLINE"] = "1"
            os.environ["HF_DATASETS_OFFLINE"] = "1"
            logger.info("设置 TRANSFORMERS_OFFLINE=1 和 HF_DATASETS_OFFLINE=1")

            logger.info(f"开始加载 BGE-M3 模型 (device=cpu, use_fp16=True)...")
            start_load_time = time.time()
            # 使用 globals 中导入的类
            globals.embedding_function = BGEM3EmbeddingFunction(
                use_fp16=False, # 使用 FP16
                device="cpu",   # 使用 CPU
                model_name_or_path=model_path # 使用本地模型路径
            )
            load_time = time.time() - start_load_time
            # 获取并存储维度
            if hasattr(globals.embedding_function, 'dim') and 'dense' in globals.embedding_function.dim:
                globals.embedding_dimension = globals.embedding_function.dim["dense"]
                logger.info(f"本地 BGE-M3 模型加载成功! 耗时: {load_time:.2f} 秒, 稠密维度: {globals.embedding_dimension}")
            else:
                 logger.warning("无法获取模型维度信息，embedding_dimension 将为 None")
                 globals.embedding_dimension = None
                 logger.info(f"本地 BGE-M3 模型加载成功! 耗时: {load_time:.2f} 秒")

        except Exception as e:
            logger.error(f"加载本地 BGE-M3 模型失败: {e}", exc_info=True)
            globals.embedding_function = None # 确保失败时为 None
            globals.embedding_dimension = None

    logger.info("应用启动完成")

    # 初始化RQ应用
    try:
        # 获取RQ实例
        rq_instance = get_rq_instance()
        logger.info("RQ应用初始化完成")

        # 输出RQ配置信息
        from .config import REDIS_HOST, REDIS_PORT, REDIS_DB
        logger.info(f"RQ配置:")
        logger.info(f"  - Redis URL: redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}")

        # 获取队列
        default_queue = get_queue("default")
        high_queue = get_queue("high")
        low_queue = get_queue("low")

        # 获取调度器
        scheduler = get_scheduler()

        logger.info(f"  - 已配置队列: default, high, low")

        # 初始化任务监控
        from .core.task_monitor import get_task_monitor
        task_monitor = get_task_monitor()
        logger.info("任务监控系统初始化完成")

        # 启动Flask-RQ调度器
        from .core.scheduler import start_scheduler, setup_default_scheduled_tasks
        start_scheduler()
        setup_default_scheduled_tasks()
        logger.info("Flask-RQ调度器已启动，默认定时任务已设置")

        # 注意：RQ Dashboard可以单独启动，但不在此处启动
        logger.info("RQ任务监控已设置，RQ Dashboard可以单独启动")
    except Exception as e:
        logger.error(f"初始化RQ应用时出错: {e}", exc_info=True)

    # 启动定时同步调度器 (保留原有的调度器，也可以完全迁移到Celery)
    from .core.sync_service import start_sync_scheduler
    try:
        start_sync_scheduler()
        logger.info("定时同步调度器已启动")
    except Exception as e:
        logger.error(f"启动定时同步调度器时出错: {e}")

# 应用关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    logger.info("应用关闭中...")

    # 关闭向量数据库连接
    try:
        from .core.vector_db import get_vector_db_client
        vector_db_client = get_vector_db_client()
        vector_db_client.close()
        logger.info("向量数据库连接已关闭")
    except Exception as e:
        logger.error(f"关闭向量数据库连接时出错: {e}")

    # 停止Flask-RQ调度器
    try:
        from .core.scheduler import stop_scheduler
        stop_scheduler()
        logger.info("Flask-RQ调度器已停止")
    except Exception as e:
        logger.error(f"停止Flask-RQ调度器时出错: {e}")

    # 停止定时同步调度器
    from .core.sync_service import stop_sync_scheduler
    try:
        stop_sync_scheduler()
        logger.info("定时同步调度器已停止")
    except Exception as e:
        logger.error(f"停止定时同步调度器时出错: {e}")

    logger.info("应用已关闭")

# 健康检查端点
@app.get("/health", tags=["system"])
async def health_check():
    return {"status": "ok", "debug_mode": true}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("api.main:app", host="0.0.0.0", port=8000, reload=True)
