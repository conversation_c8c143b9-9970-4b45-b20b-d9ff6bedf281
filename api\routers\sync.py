# 同步服务API路由

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import Dict, Any

from ..core.database import get_db
from ..core.sync_service import get_sync_service, start_sync_scheduler, stop_sync_scheduler
from ..core.task_manager import submit_task
from ..tasks.sync_tasks import sync_materials

router = APIRouter(tags=["sync"])

@router.post("/sync/start-scheduler", tags=["sync"])
async def start_scheduler(interval_minutes: int | None = None):
    """启动定时同步调度器"""
    sync_service = get_sync_service()

    # 如果提供了间隔时间，则更新同步间隔
    if interval_minutes is not None:
        sync_service.set_sync_interval(interval_minutes)

    # 启动调度器
    result = start_sync_scheduler()
    return result

@router.post("/sync/stop-scheduler", tags=["sync"])
async def stop_scheduler():
    """停止定时同步调度器"""
    result = stop_sync_scheduler()
    return result

@router.get("/sync/status", tags=["sync"])
async def get_sync_status():
    """获取同步状态"""
    sync_service = get_sync_service()
    return sync_service.get_sync_status()

@router.post("/sync/run", tags=["sync"])
async def run_sync():
    """手动触发同步"""
    sync_service = get_sync_service()

    # 检查是否已有同步任务在进行
    if sync_service.is_syncing:
        return {"status": "skipped", "reason": "已有同步任务正在进行"}

    # 使用Flask-RQ提交同步任务
    task_id = submit_task(
        sync_materials,
        queue="default",
        timeout=1800,
        description="手动触发的材料数据同步任务"
    )

    return {
        "status": "started",
        "message": "材料数据同步任务已提交到队列",
        "task_id": task_id
    }
