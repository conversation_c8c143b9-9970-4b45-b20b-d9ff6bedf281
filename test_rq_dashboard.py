#!/usr/bin/env python
"""
测试RQ Dashboard是否可以正常启动
"""

import sys
import os

def test_rq_dashboard_import():
    """测试RQ Dashboard导入"""
    print("🔍 测试RQ Dashboard导入...")
    
    try:
        import rq_dashboard
        print(f"✅ rq_dashboard导入成功，版本: {rq_dashboard.__version__}")
        
        from rq_dashboard import app
        print("✅ rq_dashboard.app导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ rq_dashboard导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_redis_connection():
    """测试Redis连接"""
    print("\n🔍 测试Redis连接...")
    
    try:
        import redis
        
        # 创建Redis连接
        redis_client = redis.Redis(
            host='***********',
            port=6379,
            password='yhb25IEz',
            db=3,
            decode_responses=False
        )
        
        # 测试连接
        redis_client.ping()
        print("✅ Redis连接成功")
        
        # 检查RQ相关键
        rq_keys = redis_client.keys("rq:*")
        print(f"✅ 找到 {len(rq_keys)} 个RQ相关键")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False

def test_rq_dashboard_config():
    """测试RQ Dashboard配置"""
    print("\n🔍 测试RQ Dashboard配置...")
    
    try:
        from rq_dashboard import app
        
        # 配置RQ Dashboard
        app.config.from_object('rq_dashboard.default_settings')
        
        # 设置Redis连接
        redis_url = 'redis://:yhb25IEz@***********:6379/3'
        app.config['RQ_DASHBOARD_REDIS_URL'] = redis_url
        
        print(f"✅ RQ Dashboard配置成功")
        print(f"📡 Redis URL: {redis_url}")
        
        # 测试配置是否生效
        configured_url = app.config.get('RQ_DASHBOARD_REDIS_URL')
        if configured_url == redis_url:
            print("✅ Redis URL配置正确")
        else:
            print(f"❌ Redis URL配置错误: {configured_url}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ RQ Dashboard配置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_manual_start():
    """手动测试启动"""
    print("\n🚀 尝试手动启动RQ Dashboard...")
    
    try:
        from rq_dashboard import app
        
        # 配置
        app.config.from_object('rq_dashboard.default_settings')
        app.config['RQ_DASHBOARD_REDIS_URL'] = 'redis://:yhb25IEz@***********:6379/3'
        
        print("✅ 配置完成，准备启动...")
        print("🌐 访问地址: http://localhost:9181")
        print("🛑 按Ctrl+C停止服务")
        print("-" * 50)
        
        # 启动（这里会阻塞）
        app.run(host='0.0.0.0', port=9181, debug=False, use_reloader=False)
        
    except KeyboardInterrupt:
        print("\n✅ 用户停止服务")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 RQ Dashboard测试开始...\n")
    
    # 测试导入
    import_ok = test_rq_dashboard_import()
    
    # 测试Redis连接
    redis_ok = test_redis_connection()
    
    # 测试配置
    config_ok = test_rq_dashboard_config()
    
    # 总结
    print("\n" + "="*50)
    print("📋 测试结果总结:")
    print(f"  RQ Dashboard导入: {'✅ 正常' if import_ok else '❌ 失败'}")
    print(f"  Redis连接: {'✅ 正常' if redis_ok else '❌ 失败'}")
    print(f"  Dashboard配置: {'✅ 正常' if config_ok else '❌ 失败'}")
    
    if import_ok and redis_ok and config_ok:
        print("\n🎉 所有测试通过！")
        
        # 询问是否启动
        try:
            choice = input("\n是否现在启动RQ Dashboard? (y/n): ").lower().strip()
            if choice == 'y':
                test_manual_start()
        except KeyboardInterrupt:
            print("\n👋 测试结束")
    else:
        print("\n⚠️  部分测试失败，请检查错误信息。")

if __name__ == "__main__":
    main()
