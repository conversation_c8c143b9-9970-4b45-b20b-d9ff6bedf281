"""
Flask-RQ定时任务调度器
替代Celery Beat，使用RQ Scheduler实现定时任务
"""

import time
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable
from rq_scheduler import Scheduler
from rq.job import Job

from .rq_app import get_redis_connection, get_queue
from .debug import get_app_logger
from .task_manager import submit_task

# 获取日志记录器
logger = get_app_logger("scheduler")

class TaskScheduler:
    """Flask-RQ定时任务调度器"""

    def __init__(self):
        """初始化调度器"""
        self.redis_connection = get_redis_connection()
        # 使用固定的调度器名称，便于管理
        self.scheduler = Scheduler(
            connection=self.redis_connection,
            name="materials_scheduler"  # 固定名称
        )
        self.scheduled_jobs = {}  # 存储已调度的任务
        self.logger = logger
        self._running = False
        self._scheduler_thread = None

    def start(self):
        """启动调度器"""
        if self._running:
            self.logger.warning("调度器已在运行")
            return

        # 清理可能存在的旧调度器实例
        self._cleanup_existing_scheduler()

        self._running = True
        self.logger.info("启动Flask-RQ定时任务调度器")

        # 在后台线程中运行调度器
        self._scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self._scheduler_thread.start()

    def stop(self):
        """停止调度器"""
        if not self._running:
            self.logger.warning("调度器未在运行")
            return

        self._running = False

        # 清理调度器注册
        try:
            self.scheduler.register_death()
            self.logger.info("调度器已注销")
        except Exception as e:
            self.logger.warning(f"注销调度器时出错: {e}")

        self.logger.info("停止Flask-RQ定时任务调度器")

    def _run_scheduler(self):
        """运行调度器的主循环"""
        # 首次启动时尝试注册
        try:
            self.scheduler.register_birth()
            self.logger.info("调度器已注册")
        except ValueError as e:
            if "already an active RQ scheduler" in str(e):
                self.logger.warning("检测到已存在的调度器，尝试清理...")
                self._cleanup_existing_scheduler()
                try:
                    self.scheduler.register_birth()
                    self.logger.info("调度器重新注册成功")
                except Exception as retry_e:
                    self.logger.error(f"重新注册调度器失败: {retry_e}")
                    self._running = False
                    return
            else:
                self.logger.error(f"注册调度器失败: {e}")
                self._running = False
                return
        except Exception as e:
            self.logger.error(f"注册调度器时出错: {e}")
            self._running = False
            return

        while self._running:
            try:
                # 处理到期的任务
                self.scheduler.run(burst=True)
                time.sleep(1)  # 每秒检查一次
            except ValueError as e:
                if "already an active RQ scheduler" in str(e):
                    self.logger.warning("调度器冲突，停止运行")
                    self._running = False
                    break
                else:
                    self.logger.error(f"调度器运行出错: {e}", exc_info=True)
                    time.sleep(5)
            except Exception as e:
                self.logger.error(f"调度器运行出错: {e}", exc_info=True)
                time.sleep(5)  # 出错时等待5秒再重试

    def _cleanup_existing_scheduler(self):
        """清理已存在的调度器实例"""
        try:
            # 获取所有调度器键
            scheduler_keys = self.redis_connection.keys("rq:scheduler:*")

            for key in scheduler_keys:
                # 检查键是否与我们的调度器相关
                if isinstance(key, bytes):
                    key_str = key.decode('utf-8', errors='ignore')
                else:
                    key_str = str(key)

                if "materials_scheduler" in key_str:
                    self.logger.info(f"清理调度器键: {key_str}")
                    self.redis_connection.delete(key)

            # 清理调度器注册表
            registry_key = "rq:scheduler:schedulers"
            if self.redis_connection.exists(registry_key):
                # 移除我们的调度器名称
                self.redis_connection.srem(registry_key, "materials_scheduler")
                self.logger.info("已从调度器注册表中移除")

        except Exception as e:
            self.logger.warning(f"清理调度器时出错: {e}")

    def schedule_task(self,
                     func: Callable,
                     scheduled_time: datetime,
                     *args,
                     job_id: str = None,
                     queue: str = "default",
                     timeout: int = 300,
                     description: str = None,
                     **kwargs) -> str:
        """
        调度单次任务

        Args:
            func: 任务函数
            scheduled_time: 调度时间
            *args: 任务参数
            job_id: 任务ID（可选）
            queue: 队列名称
            timeout: 超时时间
            description: 任务描述
            **kwargs: 其他参数

        Returns:
            任务ID
        """
        try:
            job = self.scheduler.enqueue_at(
                scheduled_time,
                func,
                *args,
                job_id=job_id,
                timeout=timeout,
                meta={'description': description, 'queue': queue},
                **kwargs
            )

            if job_id:
                self.scheduled_jobs[job_id] = job

            self.logger.info(f"已调度任务 {job.id}，执行时间: {scheduled_time}")
            return job.id

        except Exception as e:
            self.logger.error(f"调度任务失败: {e}", exc_info=True)
            raise

    def schedule_recurring_task(self,
                               func: Callable,
                               interval_seconds: int,
                               *args,
                               job_id: str = None,
                               queue: str = "default",
                               timeout: int = 300,
                               description: str = None,
                               **kwargs) -> str:
        """
        调度重复任务

        Args:
            func: 任务函数
            interval_seconds: 间隔时间（秒）
            *args: 任务参数
            job_id: 任务ID（可选）
            queue: 队列名称
            timeout: 超时时间
            description: 任务描述
            **kwargs: 其他参数

        Returns:
            任务ID
        """
        try:
            # 计算下次执行时间
            next_run = datetime.now() + timedelta(seconds=interval_seconds)

            # 创建包装函数，在执行后重新调度
            def recurring_wrapper(*wrapper_args, **wrapper_kwargs):
                try:
                    # 执行原始任务
                    result = func(*wrapper_args, **wrapper_kwargs)

                    # 重新调度下次执行
                    next_time = datetime.now() + timedelta(seconds=interval_seconds)
                    self.schedule_task(
                        recurring_wrapper,
                        next_time,
                        *wrapper_args,
                        job_id=job_id,
                        queue=queue,
                        timeout=timeout,
                        description=description,
                        **wrapper_kwargs
                    )

                    return result
                except Exception as e:
                    self.logger.error(f"重复任务执行失败: {e}", exc_info=True)
                    # 即使失败也要重新调度
                    next_time = datetime.now() + timedelta(seconds=interval_seconds)
                    self.schedule_task(
                        recurring_wrapper,
                        next_time,
                        *args,
                        job_id=job_id,
                        queue=queue,
                        timeout=timeout,
                        description=description,
                        **kwargs
                    )
                    raise

            # 调度第一次执行
            job_id = self.schedule_task(
                recurring_wrapper,
                next_run,
                *args,
                job_id=job_id,
                queue=queue,
                timeout=timeout,
                description=f"重复任务: {description}" if description else "重复任务",
                **kwargs
            )

            self.logger.info(f"已调度重复任务 {job_id}，间隔: {interval_seconds}秒")
            return job_id

        except Exception as e:
            self.logger.error(f"调度重复任务失败: {e}", exc_info=True)
            raise

    def cancel_scheduled_task(self, job_id: str) -> bool:
        """
        取消已调度的任务

        Args:
            job_id: 任务ID

        Returns:
            是否成功取消
        """
        try:
            if job_id in self.scheduled_jobs:
                job = self.scheduled_jobs[job_id]
                job.cancel()
                del self.scheduled_jobs[job_id]
                self.logger.info(f"已取消调度任务: {job_id}")
                return True
            else:
                # 尝试从调度器中取消
                self.scheduler.cancel(job_id)
                self.logger.info(f"已取消调度任务: {job_id}")
                return True

        except Exception as e:
            self.logger.error(f"取消调度任务失败: {e}", exc_info=True)
            return False

    def get_scheduled_jobs(self) -> List[Dict[str, Any]]:
        """
        获取已调度的任务列表

        Returns:
            任务列表
        """
        try:
            jobs = []
            for job in self.scheduler.get_jobs():
                job_info = {
                    "id": job.id,
                    "func_name": job.func_name,
                    "scheduled_for": job.meta.get('scheduled_for'),
                    "description": job.meta.get('description', ''),
                    "queue": job.meta.get('queue', 'default'),
                    "status": job.get_status()
                }
                jobs.append(job_info)

            return jobs

        except Exception as e:
            self.logger.error(f"获取调度任务列表失败: {e}", exc_info=True)
            return []

# 创建全局调度器实例
task_scheduler = TaskScheduler()

# 便捷函数
def start_scheduler():
    """启动调度器"""
    try:
        task_scheduler.start()
        return True
    except Exception as e:
        logger.error(f"启动调度器失败: {e}", exc_info=True)
        return False

def stop_scheduler():
    """停止调度器"""
    try:
        task_scheduler.stop()
        return True
    except Exception as e:
        logger.error(f"停止调度器失败: {e}", exc_info=True)
        return False

def schedule_task(*args, **kwargs) -> str:
    """调度单次任务"""
    return task_scheduler.schedule_task(*args, **kwargs)

def schedule_recurring_task(*args, **kwargs) -> str:
    """调度重复任务"""
    return task_scheduler.schedule_recurring_task(*args, **kwargs)

def cancel_scheduled_task(job_id: str) -> bool:
    """取消调度任务"""
    return task_scheduler.cancel_scheduled_task(job_id)

def get_scheduled_jobs() -> List[Dict[str, Any]]:
    """获取调度任务列表"""
    return task_scheduler.get_scheduled_jobs()

# 预定义的定时任务
def setup_default_scheduled_tasks():
    """设置默认的定时任务"""
    try:
        # 等待调度器启动
        import time
        time.sleep(2)

        # 检查调度器是否正在运行
        if not task_scheduler._running:
            logger.warning("调度器未运行，跳过设置默认任务")
            return False

        from ..tasks.sync_tasks import sync_materials

        # 检查是否已存在默认任务
        existing_jobs = get_scheduled_jobs()
        for job in existing_jobs:
            if job.get("id") == "daily_sync_materials":
                logger.info("默认同步任务已存在，跳过设置")
                return True

        # 每天同步一次材料数据（24小时 = 86400秒）
        task_id = schedule_recurring_task(
            sync_materials,
            86400,  # 24小时
            job_id="daily_sync_materials",
            queue="default",
            timeout=1800,
            description="每日材料数据同步任务"
        )

        logger.info(f"已设置默认定时任务: {task_id}")
        return True

    except Exception as e:
        logger.error(f"设置默认定时任务失败: {e}", exc_info=True)
        return False
