#!/usr/bin/env python
"""
调试挂载问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_mount_issue():
    """调试挂载问题"""
    print("🔍 调试FastAPI挂载问题...\n")
    
    try:
        # 1. 检查RQ Dashboard集成
        print("1. 检查RQ Dashboard集成...")
        from api.core.rq_dashboard_integration import is_rq_dashboard_available, create_rq_dashboard_wsgi
        
        available = is_rq_dashboard_available()
        print(f"   RQ Dashboard可用性: {available}")
        
        if not available:
            print("   尝试创建RQ Dashboard...")
            wsgi_app = create_rq_dashboard_wsgi()
            if wsgi_app:
                print("   ✅ RQ Dashboard WSGI应用创建成功")
            else:
                print("   ❌ RQ Dashboard WSGI应用创建失败")
                return False
        
        # 2. 检查WSGI适配器
        print("\n2. 检查WSGI适配器...")
        from api.core.wsgi_adapter import create_simple_wsgi_adapter
        
        wsgi_app = create_rq_dashboard_wsgi()
        asgi_app = create_simple_wsgi_adapter(wsgi_app)
        print(f"   ✅ ASGI适配器创建成功: {type(asgi_app)}")
        
        # 3. 检查FastAPI应用
        print("\n3. 检查FastAPI应用...")
        from api.main import app
        
        print(f"   FastAPI应用类型: {type(app)}")
        
        # 检查路由
        print("   路由列表:")
        rq_found = False
        for route in app.routes:
            if hasattr(route, 'path'):
                print(f"     - {route.path} ({type(route).__name__})")
                if route.path == "/rq":
                    rq_found = True
                    print(f"       ✅ 找到RQ挂载: {type(route.app) if hasattr(route, 'app') else 'No app'}")
            elif hasattr(route, 'path_regex'):
                print(f"     - {route.path_regex.pattern} ({type(route).__name__})")
        
        if not rq_found:
            print("   ❌ 未找到/rq挂载")
            return False
        
        # 4. 测试挂载顺序
        print("\n4. 检查挂载顺序...")
        mount_order = []
        for i, route in enumerate(app.routes):
            if hasattr(route, 'path'):
                mount_order.append(f"{i}: {route.path}")
        
        print("   挂载顺序:")
        for order in mount_order:
            print(f"     {order}")
        
        # 检查根路径挂载是否在最后
        root_mount_index = -1
        rq_mount_index = -1
        for i, route in enumerate(app.routes):
            if hasattr(route, 'path'):
                if route.path == "/":
                    root_mount_index = i
                elif route.path == "/rq":
                    rq_mount_index = i
        
        if rq_mount_index > root_mount_index:
            print("   ✅ 挂载顺序正确: /rq 在 / 之前")
        else:
            print("   ❌ 挂载顺序错误: /rq 应该在 / 之前")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_access():
    """直接测试访问"""
    print("\n🧪 直接测试访问...\n")
    
    try:
        from fastapi.testclient import TestClient
        from api.main import app
        
        client = TestClient(app)
        
        # 测试根路径
        print("1. 测试根路径...")
        response = client.get("/")
        print(f"   GET / -> {response.status_code}")
        
        # 测试API路径
        print("2. 测试API路径...")
        response = client.get("/api/health")
        print(f"   GET /api/health -> {response.status_code}")
        
        # 测试RQ路径
        print("3. 测试RQ路径...")
        response = client.get("/rq")
        print(f"   GET /rq -> {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ RQ Dashboard访问成功")
            return True
        else:
            print(f"   ❌ RQ Dashboard访问失败: {response.status_code}")
            if response.content:
                print(f"   响应内容: {response.content[:200]}")
            return False
        
    except Exception as e:
        print(f"❌ 直接访问测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 FastAPI挂载问题调试开始...\n")
    
    # 调试挂载
    mount_ok = debug_mount_issue()
    
    # 直接测试访问
    access_ok = test_direct_access()
    
    # 总结
    print("\n" + "="*50)
    print("📋 调试结果:")
    print(f"  挂载检查: {'✅ 正常' if mount_ok else '❌ 失败'}")
    print(f"  访问测试: {'✅ 正常' if access_ok else '❌ 失败'}")
    
    if mount_ok and access_ok:
        print("\n🎉 所有检查通过！")
        print("💡 如果浏览器还是404，请尝试:")
        print("   1. 重启应用")
        print("   2. 清除浏览器缓存")
        print("   3. 检查URL是否正确: http://localhost:8000/rq")
    else:
        print("\n⚠️  发现问题，请检查上述错误信息。")

if __name__ == "__main__":
    main()
