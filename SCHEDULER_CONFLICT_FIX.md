# RQ调度器冲突问题修复方案

## 🎯 问题描述

在启动Flask-RQ调度器时遇到以下错误：

```
ValueError: There's already an active RQ scheduler named: '2356ca85fd5b4731a97245b9d7cefbff'
```

这个错误发生的原因是：
1. 应用重启时，之前的调度器实例在Redis中的注册信息没有正确清理
2. RQ调度器在启动时会检查是否已有同名调度器在运行
3. 如果检测到冲突，新的调度器无法启动

## 🔧 修复方案

### 1. 使用固定的调度器名称

将随机生成的调度器名称改为固定名称，便于管理和清理：

```python
def __init__(self):
    """初始化调度器"""
    self.redis_connection = get_redis_connection()
    # 使用固定的调度器名称，便于管理
    self.scheduler = Scheduler(
        connection=self.redis_connection,
        name="materials_scheduler"  # 固定名称
    )
```

### 2. 添加调度器清理机制

在启动前清理可能存在的旧调度器实例：

```python
def _cleanup_existing_scheduler(self):
    """清理已存在的调度器实例"""
    try:
        # 获取所有调度器键
        scheduler_keys = self.redis_connection.keys("rq:scheduler:*")
        
        for key in scheduler_keys:
            # 检查键是否与我们的调度器相关
            if isinstance(key, bytes):
                key_str = key.decode('utf-8', errors='ignore')
            else:
                key_str = str(key)
            
            if "materials_scheduler" in key_str:
                self.logger.info(f"清理调度器键: {key_str}")
                self.redis_connection.delete(key)
        
        # 清理调度器注册表
        registry_key = "rq:scheduler:schedulers"
        if self.redis_connection.exists(registry_key):
            # 移除我们的调度器名称
            self.redis_connection.srem(registry_key, "materials_scheduler")
            self.logger.info("已从调度器注册表中移除")
        
    except Exception as e:
        self.logger.warning(f"清理调度器时出错: {e}")
```

### 3. 改进启动逻辑

在启动时先清理，再注册：

```python
def start(self):
    """启动调度器"""
    if self._running:
        self.logger.warning("调度器已在运行")
        return
    
    # 清理可能存在的旧调度器实例
    self._cleanup_existing_scheduler()
    
    self._running = True
    self.logger.info("启动Flask-RQ定时任务调度器")
    
    # 在后台线程中运行调度器
    self._scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
    self._scheduler_thread.start()
```

### 4. 增强运行时错误处理

在调度器运行过程中处理冲突：

```python
def _run_scheduler(self):
    """运行调度器的主循环"""
    # 首次启动时尝试注册
    try:
        self.scheduler.register_birth()
        self.logger.info("调度器已注册")
    except ValueError as e:
        if "already an active RQ scheduler" in str(e):
            self.logger.warning("检测到已存在的调度器，尝试清理...")
            self._cleanup_existing_scheduler()
            try:
                self.scheduler.register_birth()
                self.logger.info("调度器重新注册成功")
            except Exception as retry_e:
                self.logger.error(f"重新注册调度器失败: {retry_e}")
                self._running = False
                return
        else:
            self.logger.error(f"注册调度器失败: {e}")
            self._running = False
            return
    
    while self._running:
        try:
            # 处理到期的任务
            self.scheduler.run(burst=True)
            time.sleep(1)
        except ValueError as e:
            if "already an active RQ scheduler" in str(e):
                self.logger.warning("调度器冲突，停止运行")
                self._running = False
                break
            else:
                self.logger.error(f"调度器运行出错: {e}", exc_info=True)
                time.sleep(5)
        except Exception as e:
            self.logger.error(f"调度器运行出错: {e}", exc_info=True)
            time.sleep(5)
```

### 5. 改进停止逻辑

在停止时正确注销调度器：

```python
def stop(self):
    """停止调度器"""
    if not self._running:
        self.logger.warning("调度器未在运行")
        return
    
    self._running = False
    
    # 清理调度器注册
    try:
        self.scheduler.register_death()
        self.logger.info("调度器已注销")
    except Exception as e:
        self.logger.warning(f"注销调度器时出错: {e}")
    
    self.logger.info("停止Flask-RQ定时任务调度器")
```

### 6. 安全的默认任务设置

避免在调度器未完全启动时设置任务：

```python
def setup_default_scheduled_tasks():
    """设置默认的定时任务"""
    try:
        # 等待调度器启动
        import time
        time.sleep(2)
        
        # 检查调度器是否正在运行
        if not task_scheduler._running:
            logger.warning("调度器未运行，跳过设置默认任务")
            return False
        
        # 检查是否已存在默认任务
        existing_jobs = get_scheduled_jobs()
        for job in existing_jobs:
            if job.get("id") == "daily_sync_materials":
                logger.info("默认同步任务已存在，跳过设置")
                return True
        
        # 设置新的默认任务
        task_id = schedule_recurring_task(
            sync_materials,
            86400,  # 24小时
            job_id="daily_sync_materials",
            queue="default",
            timeout=1800,
            description="每日材料数据同步任务"
        )
        
        logger.info(f"已设置默认定时任务: {task_id}")
        return True
        
    except Exception as e:
        logger.error(f"设置默认定时任务失败: {e}", exc_info=True)
        return False
```

## 📋 修复的文件

1. **api/core/scheduler.py**
   - 使用固定调度器名称
   - 添加清理机制
   - 改进启动和停止逻辑
   - 增强错误处理

2. **api/main.py**
   - 改进调度器启动逻辑
   - 使用后台线程设置默认任务
   - 增强错误处理

## 🎯 解决的问题

### 1. 调度器冲突
- **问题**: 重启时调度器名称冲突
- **解决**: 使用固定名称 + 启动前清理

### 2. 注册信息残留
- **问题**: Redis中的调度器注册信息没有清理
- **解决**: 主动清理相关键和注册表

### 3. 启动失败
- **问题**: 冲突导致调度器无法启动
- **解决**: 多层错误处理和重试机制

### 4. 任务设置时机
- **问题**: 在调度器未完全启动时设置任务
- **解决**: 等待调度器启动 + 后台线程设置

## 🚀 使用效果

修复后的调度器具有以下特点：

1. **自动恢复** - 能够自动清理冲突并重新启动
2. **稳定运行** - 处理各种异常情况
3. **优雅停止** - 正确注销和清理资源
4. **防重复** - 避免重复设置默认任务

## 📝 注意事项

### 1. 调度器名称
- 使用固定名称 `materials_scheduler`
- 便于识别和管理

### 2. 清理策略
- 只清理与当前应用相关的调度器
- 不影响其他应用的调度器

### 3. 错误处理
- 多层错误处理，确保系统稳定
- 详细的日志记录，便于问题排查

### 4. 性能影响
- 清理操作很轻量，对性能影响极小
- 使用后台线程避免阻塞主流程

## 🎉 总结

通过这次修复，我们解决了：

1. ✅ **调度器冲突** - 不再出现名称冲突错误
2. ✅ **启动稳定性** - 调度器能够稳定启动
3. ✅ **资源清理** - 正确清理Redis中的注册信息
4. ✅ **错误恢复** - 具备自动恢复能力
5. ✅ **任务管理** - 默认任务正确设置

现在Flask-RQ调度器可以稳定运行，为统一任务管理系统提供可靠的定时任务功能。
